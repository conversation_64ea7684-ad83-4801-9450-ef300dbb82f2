from __future__ import annotations

import unittest
from unittest import mock

from flask import Flask

from superset.views.utils import get_user_dept_id


class TestUserDeptId(unittest.TestCase):
    """Tests for the get_user_dept_id function."""

    def setUp(self):
        """Set up Flask app context and request context for tests."""
        self.app = Flask(__name__)
        self.app.config["SECRET_KEY"] = "test-secret"
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.request_context = self.app.test_request_context()
        self.request_context.push()

    def tearDown(self):
        """Clean up Flask contexts."""
        self.request_context.pop()
        self.app_context.pop()

    @mock.patch("superset.views.utils.session", new_callable=mock.MagicMock)
    def test_get_user_dept_id_with_depid(self, mock_session):
        """Test get_user_dept_id when depid is in session."""
        # Mock session to return depid
        mock_session.get.side_effect = (
            lambda key, default=None: "DEP123" if key == "depid" else default
        )

        # Call the function
        result = get_user_dept_id()

        # Verify the result is the depid
        self.assertEqual(result, ["DEP123"])
        # Verify session.get was called with 'depid'
        mock_session.get.assert_any_call("depid")

    @mock.patch("superset.views.utils.session", new_callable=mock.MagicMock)
    @mock.patch("superset.views.utils.OrgUnitDAO")
    def test_get_user_dept_id_with_dobr_dmid(self, mock_dao, mock_session):
        """Test get_user_dept_id when depid is not in session but dobr and dmid are."""

        # Mock session to return None for depid but provide dobr and dmid
        def session_get_side_effect(key, default=None):
            if key == "depid":
                return None
            elif key == "d:obr":
                return ["CLAIM1:1:3", "CLAIM2:3:1"]
            elif key == "d:mid":
                return "MID123"
            return default

        mock_session.get.side_effect = session_get_side_effect

        # Mock OrgUnitDAO.get_departments_from_claims to return departments
        mock_dao.get_departments_from_claims.return_value = ["DEP1", "DEP2"]

        # Call the function
        result = get_user_dept_id()

        # Verify OrgUnitDAO.get_departments_from_claims was called with dobr and dmid
        mock_dao.get_departments_from_claims.assert_called_once_with(
            ["CLAIM1:1:3", "CLAIM2:3:1"], "MID123"
        )

        # Verify the result contains the departments
        self.assertEqual(result, ["DEP1", "DEP2"])

    @mock.patch("superset.views.utils.session", new_callable=mock.MagicMock)
    @mock.patch("superset.views.utils.OrgUnitDAO")
    def test_get_user_dept_id_no_claims(self, mock_dao, mock_session):
        """Test get_user_dept_id when no claims are available."""
        # Mock session to return None for all keys
        mock_session.get.return_value = None

        # Mock OrgUnitDAO.get_departments_from_claims to return empty list
        mock_dao.get_departments_from_claims.return_value = []

        # Call the function
        result = get_user_dept_id()

        # Verify OrgUnitDAO.get_departments_from_claims was called with empty claims
        mock_dao.get_departments_from_claims.assert_called_once_with([], None)

        # Verify the result is an empty list
        self.assertEqual(result, [])

    @mock.patch("superset.views.utils.session", new_callable=mock.MagicMock)
    @mock.patch("superset.views.utils.OrgUnitDAO")
    def test_get_user_dept_id_empty_dobr(self, mock_dao, mock_session):
        """Test get_user_dept_id with empty dobr list."""

        # Mock session to return empty dobr
        def session_get_side_effect(key, default=None):
            if key == "depid":
                return None
            elif key == "d:obr":
                return []  # Empty list
            elif key == "d:mid":
                return "MID123"
            return default

        mock_session.get.side_effect = session_get_side_effect

        # Call the function
        get_user_dept_id()

        # Verify OrgUnitDAO.get_departments_from_claims was called with empty dobr
        mock_dao.get_departments_from_claims.assert_called_once_with([], "MID123")
