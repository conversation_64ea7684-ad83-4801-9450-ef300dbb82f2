# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from unittest.mock import MagicMock, Mock, patch

import pytest
from flask_appbuilder.security.sqla.models import User

from superset.folders.models import Folder, FolderItemType, FolderLevel, FolderType
from superset.folders.utils import (
    _get_child_folders,
    add_order_to_content,
    calculate_folder_diff,
    validate_folder_content,
)


class TestGetChildFolders:
    """Test cases for get_child_folders function."""

    def test_single_folder_no_children(self):
        """Test with a single folder that has no children."""
        folder = Mock(spec=Folder)
        folder.id = 1
        folder.parent_id = None

        result = _get_child_folders(1, [folder])
        assert result == [1]

    def test_folder_with_direct_children(self):
        """Test with a folder that has direct children."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=2, parent_id=1),
            Mock(spec=Folder, id=3, parent_id=1),
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1, 2, 3}

    def test_deep_hierarchy(self):
        """Test with a deep hierarchy of folders."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=2, parent_id=1),
            Mock(spec=Folder, id=3, parent_id=2),
            Mock(spec=Folder, id=4, parent_id=3),
            Mock(spec=Folder, id=5, parent_id=4),
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1, 2, 3, 4, 5}

    def test_user_provided_example(self):
        """Test with the specific example provided by the user."""
        # Folders: 1/None, 27/5, 26/27, 28/26
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=27, parent_id=5),
            Mock(spec=Folder, id=26, parent_id=27),
            Mock(spec=Folder, id=28, parent_id=26),
        ]

        # Test starting from folder 1
        result = _get_child_folders(1, folders)
        assert set(result) == {1}  # Only folder 1, since 27's parent is 5, not 1

        # Test starting from folder 5 (which would include the chain 5->27->26->28)
        result = _get_child_folders(5, folders)
        assert set(result) == {5, 27, 26, 28}

    def test_unordered_folders(self):
        """Test that the function works regardless of folder order."""
        # Same folders as above but in different order
        folders = [
            Mock(spec=Folder, id=28, parent_id=26),
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=26, parent_id=27),
            Mock(spec=Folder, id=27, parent_id=5),
        ]

        result = _get_child_folders(5, folders)
        assert set(result) == {5, 27, 26, 28}

    def test_multiple_root_folders(self):
        """Test with multiple root folders (parent_id=None)."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=2, parent_id=None),
            Mock(spec=Folder, id=3, parent_id=1),
            Mock(spec=Folder, id=4, parent_id=2),
            Mock(spec=Folder, id=5, parent_id=3),
        ]

        # Test starting from folder 1
        result = _get_child_folders(1, folders)
        assert set(result) == {1, 3, 5}

        # Test starting from folder 2
        result = _get_child_folders(2, folders)
        assert set(result) == {2, 4}

    def test_branching_hierarchy(self):
        """Test with a branching hierarchy."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=2, parent_id=1),
            Mock(spec=Folder, id=3, parent_id=1),
            Mock(spec=Folder, id=4, parent_id=2),
            Mock(spec=Folder, id=5, parent_id=2),
            Mock(spec=Folder, id=6, parent_id=3),
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1, 2, 3, 4, 5, 6}

    def test_nonexistent_parent(self):
        """Test with a parent_id that doesn't exist in the folders list."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=2, parent_id=1),
        ]

        result = _get_child_folders(999, folders)
        assert result == [999]

    def test_empty_folders_list(self):
        """Test with an empty folders list."""
        result = _get_child_folders(1, [])
        assert result == [1]

    def test_self_referential_folder(self):
        """Test with a folder that references itself as parent (edge case)."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=1),  # Self-referential
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1}

    def test_duplicate_folder_ids(self):
        """Test with duplicate folder IDs (should not cause issues)."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),
            Mock(spec=Folder, id=1, parent_id=None),  # Duplicate
            Mock(spec=Folder, id=2, parent_id=1),
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1, 2}

    def test_complex_real_world_scenario(self):
        """Test with a complex real-world scenario."""
        folders = [
            Mock(spec=Folder, id=1, parent_id=None),  # Root
            Mock(spec=Folder, id=10, parent_id=1),  # Level 1
            Mock(spec=Folder, id=11, parent_id=1),  # Level 1
            Mock(spec=Folder, id=20, parent_id=10),  # Level 2
            Mock(spec=Folder, id=21, parent_id=10),  # Level 2
            Mock(spec=Folder, id=30, parent_id=20),  # Level 3
            Mock(spec=Folder, id=31, parent_id=21),  # Level 3
            Mock(spec=Folder, id=40, parent_id=30),  # Level 4
        ]

        result = _get_child_folders(1, folders)
        assert set(result) == {1, 10, 11, 20, 21, 30, 31, 40}

        # Test starting from a middle level
        result = _get_child_folders(10, folders)
        assert set(result) == {10, 20, 21, 30, 31, 40}


class TestFolderUtils:
    def setup_method(self):
        self.user = MagicMock(spec=User)
        self.user.id = 1

        # Create mock folders for testing
        self.parent_folder = MagicMock(spec=Folder)
        self.parent_folder.id = 1
        self.parent_folder.type = FolderType.GLOBAL.value
        self.parent_folder.level = FolderLevel.SYSTEM.value

        self.child_folder1 = MagicMock(spec=Folder)
        self.child_folder1.id = 2
        self.child_folder1.parent_id = self.parent_folder.id
        self.child_folder1.type = FolderType.GLOBAL.value
        self.child_folder1.level = FolderLevel.USER.value

        self.child_folder2 = MagicMock(spec=Folder)
        self.child_folder2.id = 3
        self.child_folder2.parent_id = self.parent_folder.id
        self.child_folder2.type = FolderType.GLOBAL.value
        self.child_folder2.level = FolderLevel.USER.value

        self.grandchild_folder = MagicMock(spec=Folder)
        self.grandchild_folder.id = 4
        self.grandchild_folder.parent_id = self.child_folder1.id
        self.grandchild_folder.type = FolderType.GLOBAL.value
        self.grandchild_folder.level = FolderLevel.USER.value

        self.unrelated_folder = MagicMock(spec=Folder)
        self.unrelated_folder.id = 5
        self.unrelated_folder.parent_id = 999  # Different parent
        self.unrelated_folder.type = FolderType.GLOBAL.value
        self.unrelated_folder.level = FolderLevel.USER.value

    def test_add_order_to_content(self):
        # Test with empty list
        assert add_order_to_content([]) == []

        # Test with list of items
        content = [
            {"type": 0, "id": 1},
            {"type": 1, "id": 2},
            {"type": 0, "id": 3},
        ]
        result = add_order_to_content(content)

        assert len(result) == 3
        assert result[0]["order"] == 0
        assert result[1]["order"] == 1
        assert result[2]["order"] == 2

        # Test with existing order values (should be overwritten)
        content = [
            {"type": 0, "id": 1, "order": 10},
            {"type": 1, "id": 2, "order": 20},
        ]
        result = add_order_to_content(content)

        assert len(result) == 2
        assert result[0]["order"] == 0
        assert result[1]["order"] == 1

    def test_calculate_folder_diff(self):
        # Test with identical content
        current = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
        ]
        new = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
        ]

        diff = calculate_folder_diff(current, new)
        assert len(diff["add"]) == 0
        assert len(diff["remove"]) == 0
        assert len(diff["update"]) == 0

        # Test with added items
        current = [
            {"type": 0, "id": 1, "order": 0},
        ]
        new = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
        ]

        diff = calculate_folder_diff(current, new)
        assert len(diff["add"]) == 1
        assert diff["add"][0]["id"] == 2
        assert len(diff["remove"]) == 0
        assert len(diff["update"]) == 0

        # Test with removed items
        current = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
        ]
        new = [
            {"type": 0, "id": 1, "order": 0},
        ]

        diff = calculate_folder_diff(current, new)
        assert len(diff["add"]) == 0
        assert len(diff["remove"]) == 1
        assert diff["remove"][0]["id"] == 2
        assert len(diff["update"]) == 0

        # Test with reordered items
        current = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
        ]
        new = [
            {"type": 1, "id": 2, "order": 0},
            {"type": 0, "id": 1, "order": 1},
        ]

        diff = calculate_folder_diff(current, new)
        assert len(diff["add"]) == 0
        assert len(diff["remove"]) == 0
        assert len(diff["update"]) == 2

        # Test complex case: add, remove, and reorder
        current = [
            {"type": 0, "id": 1, "order": 0},
            {"type": 1, "id": 2, "order": 1},
            {"type": 0, "id": 3, "order": 2},
        ]
        new = [
            {"type": 1, "id": 2, "order": 0},
            {"type": 0, "id": 4, "order": 1},
            {"type": 0, "id": 1, "order": 2},
        ]

        diff = calculate_folder_diff(current, new)
        assert len(diff["add"]) == 1
        assert diff["add"][0]["id"] == 4
        assert len(diff["remove"]) == 1
        assert diff["remove"][0]["id"] == 3
        assert len(diff["update"]) == 2
        assert any(item["id"] == 1 and item["order"] == 2 for item in diff["update"])
        assert any(item["id"] == 2 and item["order"] == 0 for item in diff["update"])

    @patch("superset.folders.utils.DashboardDAO")
    @patch("superset.folders.utils.FolderDAO")
    @patch("superset.folders.utils.can_modify_folder")
    def test_validate_folder_content_duplicate_content(
        self, mock_can_modify_folder, mock_folder_dao, mock_dashboard_dao
    ):
        # Set up mocks
        mock_folder = MagicMock()
        mock_dashboard = MagicMock()
        mock_folder_dao.find_by_id.return_value = mock_folder
        mock_dashboard_dao.find_by_id.return_value = mock_dashboard
        mock_can_modify_folder.return_value = True

        # Important: Make sure the mock folder has the same type as parent_folder
        mock_folder.type = self.parent_folder.type

        # Test with duplicate content
        content = [
            {"type": FolderItemType.FOLDER.value, "id": 1},
            {"type": FolderItemType.FOLDER.value, "id": 1},
        ]

        with pytest.raises(ValueError, match="Content is not unique"):
            validate_folder_content(self.parent_folder, content, self.user)

    @patch("superset.folders.utils.DashboardDAO")
    @patch("superset.folders.utils.FolderDAO")
    @patch("superset.folders.utils.can_modify_folder")
    def test_validate_folder_content_item_not_found(
        self, mock_can_modify_folder, mock_folder_dao, mock_dashboard_dao
    ):
        # Set up mocks - return None to simulate item not found
        mock_folder_dao_instance = MagicMock()
        mock_folder_dao_instance.find_by_id.return_value = None
        mock_folder_dao.return_value = mock_folder_dao_instance

        mock_dashboard_dao_instance = MagicMock()
        mock_dashboard_dao_instance.find_by_id.return_value = None
        mock_dashboard_dao.return_value = mock_dashboard_dao_instance

        mock_can_modify_folder.return_value = True

        # Test with folder item not found
        content = [{"type": FolderItemType.FOLDER.value, "id": 999}]

        with pytest.raises(ValueError, match="Item .* not found"):
            validate_folder_content(self.parent_folder, content, self.user)

        # Test with dashboard item not found
        content = [{"type": FolderItemType.DASHBOARD.value, "id": 999}]

        with pytest.raises(ValueError, match="Item .* not found"):
            validate_folder_content(self.parent_folder, content, self.user)

    @patch("superset.folders.utils.DashboardDAO")
    @patch("superset.folders.utils.FolderDAO")
    @patch("superset.folders.utils.can_modify_folder")
    def test_validate_folder_content_permission_error(
        self, mock_can_modify_folder, mock_folder_dao, mock_dashboard_dao
    ):
        # Set up mocks
        mock_folder = MagicMock()
        mock_folder.type = self.parent_folder.type  # Same type as parent

        mock_folder_dao_instance = MagicMock()
        mock_folder_dao_instance.find_by_id.return_value = mock_folder
        mock_folder_dao.return_value = mock_folder_dao_instance

        mock_can_modify_folder.return_value = False  # No permission

        # Test with permission denied
        content = [{"type": FolderItemType.FOLDER.value, "id": 1}]

        with pytest.raises(PermissionError, match="You are not allowed to update"):
            validate_folder_content(self.parent_folder, content, self.user)

    @patch("superset.folders.utils.DashboardDAO")
    @patch("superset.folders.utils.FolderDAO")
    @patch("superset.folders.utils.can_modify_folder")
    def test_validate_folder_content_wrong_type(
        self, mock_can_modify_folder, mock_folder_dao, mock_dashboard_dao
    ):
        # Set up mocks
        mock_folder = MagicMock()
        mock_folder_dao.find_by_id.return_value = mock_folder
        mock_can_modify_folder.return_value = True

        # Different type than parent folder
        mock_folder.type = (
            FolderType.PERSONAL.value
        )  # Different from parent_folder.type

        # Test with wrong type
        content = [{"type": FolderItemType.FOLDER.value, "id": 1}]

        with pytest.raises(ValueError, match="is not of type parent folder"):
            validate_folder_content(self.parent_folder, content, self.user)

    @patch("superset.folders.utils.DashboardDAO")
    @patch("superset.folders.utils.FolderDAO")
    @patch("superset.folders.utils.can_modify_folder")
    def test_validate_folder_content_success(
        self, mock_can_modify_folder, mock_folder_dao, mock_dashboard_dao
    ):
        # Set up mocks
        mock_folder = MagicMock()
        mock_folder.type = self.parent_folder.type  # Same type as parent

        mock_dashboard = MagicMock()

        mock_folder_dao_instance = MagicMock()
        mock_folder_dao_instance.find_by_id.return_value = mock_folder
        mock_folder_dao.return_value = mock_folder_dao_instance

        mock_dashboard_dao_instance = MagicMock()
        mock_dashboard_dao_instance.find_by_id.return_value = mock_dashboard
        mock_dashboard_dao.return_value = mock_dashboard_dao_instance

        mock_can_modify_folder.return_value = True

        # Test with valid content
        content = [
            {"type": FolderItemType.FOLDER.value, "id": 1},
            {"type": FolderItemType.DASHBOARD.value, "id": 2},
        ]

        # Should not raise any exception
        validate_folder_content(self.parent_folder, content, self.user)
