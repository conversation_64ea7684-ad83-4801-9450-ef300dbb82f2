# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from unittest.mock import MagicMock, patch

from flask_appbuilder.security.sqla.models import User

from superset.folders.models import Folder, FOLDER_ADMIN_ROLE, FolderLevel, FolderType
from superset.folders.security import (
    can_modify_folder,
    can_update_folder_content,
    has_folders_admin_permission,
)


class TestFolderSecurity:
    def setup_method(self):
        self.user = MagicMock(spec=User)
        self.user.id = 1

        # Create mock folders for testing
        self.root_global_folder = MagicMock(spec=Folder)
        self.root_global_folder.id = 1
        self.root_global_folder.type = FolderType.GLOBAL.value
        self.root_global_folder.level = FolderLevel.ROOT.value

        self.system_global_folder = MagicMock(spec=Folder)
        self.system_global_folder.id = 2
        self.system_global_folder.type = FolderType.GLOBAL.value
        self.system_global_folder.level = FolderLevel.SYSTEM.value

        self.user_global_folder = MagicMock(spec=Folder)
        self.user_global_folder.id = 3
        self.user_global_folder.type = FolderType.GLOBAL.value
        self.user_global_folder.level = FolderLevel.USER.value

        self.system_personal_folder = MagicMock(spec=Folder)
        self.system_personal_folder.id = 4
        self.system_personal_folder.type = FolderType.PERSONAL.value
        self.system_personal_folder.level = FolderLevel.SYSTEM.value
        self.system_personal_folder.created_by_fk = self.user.id

        self.user_personal_folder = MagicMock(spec=Folder)
        self.user_personal_folder.id = 5
        self.user_personal_folder.type = FolderType.PERSONAL.value
        self.user_personal_folder.level = FolderLevel.USER.value
        self.user_personal_folder.created_by_fk = self.user.id

        self.user_personal_folder_other_user = MagicMock(spec=Folder)
        self.user_personal_folder_other_user.id = 6
        self.user_personal_folder_other_user.type = FolderType.PERSONAL.value
        self.user_personal_folder_other_user.level = FolderLevel.USER.value
        self.user_personal_folder_other_user.created_by_fk = 999  # Different user

        self.system_plugin_folder = MagicMock(spec=Folder)
        self.system_plugin_folder.id = 7
        self.system_plugin_folder.type = FolderType.PLUGIN.value
        self.system_plugin_folder.level = FolderLevel.SYSTEM.value

        self.user_plugin_folder = MagicMock(spec=Folder)
        self.user_plugin_folder.id = 8
        self.user_plugin_folder.type = FolderType.PLUGIN.value
        self.user_plugin_folder.level = FolderLevel.USER.value

    @patch("superset.folders.security.security_manager")
    def test_has_folders_admin_permission_plugin(self, mock_security_manager):
        # Test with plugin admin role
        mock_security_manager.is_admin.return_value = False
        mock_security_manager.has_role.return_value = True
        assert has_folders_admin_permission(FolderType.PLUGIN) is True
        mock_security_manager.has_role.assert_called_with(FOLDER_ADMIN_ROLE[FolderType.PLUGIN])

    @patch("superset.folders.security.has_folders_admin_permission")
    def test_can_modify_folder(self, mock_has_folders_admin_permission):
        # ROOT level folders cannot be modified by anyone
        assert can_modify_folder(self.root_global_folder, self.user) is False

        # SYSTEM level folders cannot be modified by anyone
        assert can_modify_folder(self.system_global_folder, self.user) is False

        # USER level PERSONAL folders can be modified by owner
        assert can_modify_folder(self.user_personal_folder, self.user) is True

        # USER level PERSONAL folders cannot be modified by non-owner
        assert (
            can_modify_folder(self.user_personal_folder_other_user, self.user) is False
        )

        # USER level GLOBAL folders can be modified by admin
        mock_has_folders_admin_permission.return_value = True
        assert can_modify_folder(self.user_global_folder, self.user) is True
        mock_has_folders_admin_permission.assert_called_with(FolderType.GLOBAL)

        # USER level GLOBAL folders cannot be modified by non-admin
        mock_has_folders_admin_permission.return_value = False
        assert can_modify_folder(self.user_global_folder, self.user) is False

    @patch("superset.folders.security.has_folders_admin_permission")
    def test_can_update_folder_content(self, mock_has_folders_admin_permission):
        # ROOT level folders cannot have content updated
        assert can_update_folder_content(self.root_global_folder, self.user) is False

        # SYSTEM level GLOBAL folders can have content updated by admin
        mock_has_folders_admin_permission.return_value = True
        assert can_update_folder_content(self.system_global_folder, self.user) is True
        mock_has_folders_admin_permission.assert_called_with(FolderType.GLOBAL)

        # SYSTEM level GLOBAL folders cannot have content updated by non-admin
        mock_has_folders_admin_permission.return_value = False
        assert can_update_folder_content(self.system_global_folder, self.user) is False

        # SYSTEM level PERSONAL folders can have content updated by anyone
        assert can_update_folder_content(self.system_personal_folder, self.user) is True

        # USER level PERSONAL folders can have content updated by owner
        assert can_update_folder_content(self.user_personal_folder, self.user) is True

        # USER level PERSONAL folders cannot have content updated by non-owner
        assert (
            can_update_folder_content(self.user_personal_folder_other_user, self.user)
            is False
        )

        # USER level PLUGIN folders can have content updated by admin
        mock_has_folders_admin_permission.return_value = True
        assert can_update_folder_content(self.user_plugin_folder, self.user) is True
        mock_has_folders_admin_permission.assert_called_with(FolderType.PLUGIN)

        # USER level PLUGIN folders cannot have content updated by non-admin
        mock_has_folders_admin_permission.return_value = False
        assert can_update_folder_content(self.user_plugin_folder, self.user) is False
