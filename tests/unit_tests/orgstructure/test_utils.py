from __future__ import annotations

import unittest
from unittest import mock

from superset.orgstructure.models import ClaimType
from superset.orgstructure.utils import parse_claim, parse_unit


class TestUtils(unittest.TestCase):
    """Test cases for the utils module."""

    def test_parse_claim_valid(self):
        """Test parsing a valid claim."""
        # Format: uuid:type:role
        claim = "000D3A21912A80E711E69F62D4D4BAC1:1:3"
        parsed_claim = parse_claim(claim)
        uuid, type_enum, role = parsed_claim

        self.assertEqual(uuid, "000D3A21912A80E711E69F62D4D4BAC1")
        self.assertEqual(type_enum, ClaimType.DEPARTMENT)
        self.assertEqual(role, "3")

    def test_parse_claim_country(self):
        """Test parsing a country claim."""
        claim = "643:3:1"  # Country claim
        parsed_claim = parse_claim(claim)
        uuid, type_enum, role = parsed_claim

        self.assertEqual(uuid, "643")
        self.assertEqual(type_enum, ClaimType.COUNTRY)
        self.assertEqual(role, "1")

    def test_parse_claim_unit(self):
        """Test parsing a unit claim."""
        claim = "000D3A21912A80E711E69F62D4D4BAC1:2:4"  # Unit claim
        parsed_claim = parse_claim(claim)
        uuid, type_enum, role = parsed_claim

        self.assertEqual(uuid, "000D3A21912A80E711E69F62D4D4BAC1")
        self.assertEqual(type_enum, ClaimType.UNIT)
        self.assertEqual(role, "4")

    def test_parse_claim_invalid(self):
        """Test parsing an invalid claim."""
        # Invalid format
        claim = "invalid_claim"
        self.assertIsNone(parse_claim(claim))

        # Invalid type
        claim = "000D3A21912A80E711E69F62D4D4BAC1:5:3"  # Type 5 doesn't exist
        self.assertIsNone(parse_claim(claim))

    @mock.patch("superset.orgstructure.utils.UnitSchema")
    def test_parse_unit_valid(self, mock_schema_class):
        """Test parsing a valid unit from a message."""
        # Mock the schema to return a validated object
        mock_schema_instance = mock.MagicMock()
        mock_schema_class.return_value = mock_schema_instance
        mock_schema_instance.load.return_value = {
            "id": "TEST123",
            "country_id": 123,
            "department_uuid": "dep-uuid-123",
            "name": "Test Unit",
            "alias": "Test",
            "type": 1,
            "is_removed": False,
            "version": 5,
            "monolith_id": "m123",
        }

        # Test data
        unit_type_header = "Dodo.DataCatalog.Contracts.OrganizationalStructure.v1.Unit"
        data = {
            "Id": "TEST123",
            "CountryId": 123,
            "Type": 1,
            # Other fields would be here
        }

        result = parse_unit(unit_type_header, data)

        # Verify the schema was called with the right data
        mock_schema_instance.load.assert_called_once()
        # Verify Type was updated in the data
        self.assertEqual(data["Type"], 1)
        # Verify the result is the validated data
        self.assertEqual(result["id"], "TEST123")
        self.assertEqual(result["country_id"], 123)

    @mock.patch("superset.orgstructure.utils.UnitSchema")
    def test_parse_unit_pizzeria(self, mock_schema_class):
        """Test parsing a pizzeria unit from a message."""
        # Mock the schema
        mock_schema_instance = mock.MagicMock()
        mock_schema_class.return_value = mock_schema_instance
        mock_schema_instance.load.return_value = {"id": "PIZZA1", "type": 1}

        # Test data
        unit_type_header = "Dodo.DataCatalog.Contracts.OrganizationalStructure.v1.Store"
        data = {"Id": "PIZZA1"}

        parse_unit(unit_type_header, data)

        # Verify Type was set to PIZZERIA
        self.assertEqual(data["Type"], 1)  # 1 = PIZZERIA

    @mock.patch("superset.orgstructure.utils.UnitSchema")
    def test_parse_unit_distribution_center(self, mock_schema_class):
        """Test parsing a distribution center unit from a message."""
        # Mock the schema
        mock_schema_instance = mock.MagicMock()
        mock_schema_class.return_value = mock_schema_instance
        mock_schema_instance.load.return_value = {"id": "DC1", "type": 6}

        # Test data
        unit_type_header = (
            "Dodo.DataCatalog.Contracts.OrganizationalStructure.v2.DistributionCenter"
        )
        data = {"Id": "DC1"}

        parse_unit(unit_type_header, data)

        # Verify Type was set to PRODUCTION_DISTRIBUTION_WORKSHOP
        self.assertEqual(data["Type"], 6)  # 6 = PRODUCTION_DISTRIBUTION_WORKSHOP

    def test_parse_unit_invalid_type(self):
        """Test parsing a unit with an invalid type."""
        # Test data with invalid type
        unit_type_header = "Invalid.Type"
        data = {"Id": "TEST123"}

        result = parse_unit(unit_type_header, data)

        # Should return None for invalid type
        self.assertIsNone(result)

    @mock.patch("superset.orgstructure.utils.UnitSchema")
    def test_parse_unit_validation_error(self, mock_schema_class):
        """Test handling of validation error during unit parsing."""
        # Mock the schema to raise a validation error
        mock_schema_instance = mock.MagicMock()
        mock_schema_class.return_value = mock_schema_instance
        mock_schema_instance.load.side_effect = Exception("Validation error")

        # Test data
        unit_type_header = "Dodo.DataCatalog.Contracts.OrganizationalStructure.v1.Unit"
        data = {"Id": "TEST123", "Type": 1}

        result = parse_unit(unit_type_header, data)

        # Should return None on validation error
        self.assertIsNone(result)
