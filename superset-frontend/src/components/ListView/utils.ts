// DODO was here
import { useEffect, useMemo, useState, useRef, ReactNode } from 'react';
import {
  useFilters,
  usePagination,
  useRowSelect,
  useRowState,
  useSortBy,
  useTable,
} from 'react-table';

import {
  NumberParam,
  StringParam,
  useQueryParams,
  QueryParamConfig,
} from 'use-query-params';

import rison from 'rison';
import { isEqual } from 'lodash';
import {
  FetchDataConfig,
  Filter,
  FilterValue,
  InternalFilter,
  SortColumn,
  ViewModeType,
} from './types';

// Define custom RisonParam for proper encoding/decoding; note that
// %, &, +, and # must be encoded to avoid breaking the url
const RisonParam: QueryParamConfig<string, any> = {
  encode: (data?: any | null) =>
    data === undefined
      ? undefined
      : rison
          .encode(data)
          .replace(/%/g, '%25')
          .replace(/&/g, '%26')
          .replace(/\+/g, '%2B')
          .replace(/#/g, '%23'),
  decode: (dataStr?: string | string[]) =>
    dataStr === undefined || Array.isArray(dataStr)
      ? undefined
      : rison.decode(dataStr),
};

export const SELECT_WIDTH = 200;

export class ListViewError extends Error {
  name = 'ListViewError';
}

// removes element from a list, returns new list
export function removeFromList(list: any[], index: number): any[] {
  return list.filter((_, i) => index !== i);
}

// apply update to elements of object list, returns new list
function updateInList(list: any[], index: number, update: any): any[] {
  const element = list.find((_, i) => index === i);

  return [
    ...list.slice(0, index),
    { ...element, ...update },
    ...list.slice(index + 1),
  ];
}

type QueryFilterState = {
  [id: string]: FilterValue['value'];
};

function mergeCreateFilterValues(list: Filter[], updateObj: QueryFilterState) {
  return list.map(({ id, urlDisplay, operator }) => {
    const currentFilterId = urlDisplay || id;
    const update = updateObj[currentFilterId];

    return { id, urlDisplay, operator, value: update };
  });
}

// convert filters from UI objects to data objects
export function convertFilters(fts: InternalFilter[]): FilterValue[] {
  return fts
    .filter(
      f =>
        !(
          typeof f.value === 'undefined' ||
          (Array.isArray(f.value) && !f.value.length)
        ),
    )
    .map(({ value, operator, id }) => {
      // handle between filter using 2 api filters
      if (operator === 'between' && Array.isArray(value)) {
        return [
          {
            value: value[0],
            operator: 'gt',
            id,
          },
          {
            value: value[1],
            operator: 'lt',
            id,
          },
        ];
      }
      return {
        value,
        operator,
        id,
      };
    })
    .flat();
}

// convertFilters but to handle new decoded rison format
export function convertFiltersRison(
  filterObj: any,
  list: Filter[],
): FilterValue[] {
  const filters: FilterValue[] = [];
  const refs = {};

  Object.keys(filterObj).forEach(id => {
    const filter: FilterValue = {
      id,
      value: filterObj[id],
      // operator: filterObj[id][1], // TODO: can probably get rid of this
    };

    refs[id] = filter;
    filters.push(filter);
  });

  // Add operators from filter list
  list.forEach(value => {
    const currentFilterId = value.urlDisplay || value.id;
    const filter = refs[currentFilterId];

    if (filter) {
      filter.operator = value.operator;
      filter.id = value.id;
    }
  });

  return filters;
}

export function extractInputValue(inputType: Filter['input'], event: any) {
  if (!inputType || inputType === 'text') {
    return event.currentTarget.value;
  }
  if (inputType === 'checkbox') {
    return event.currentTarget.checked;
  }

  return null;
}

interface UseListViewConfig {
  fetchData: (conf: FetchDataConfig) => any;
  columns: any[];
  data: any[];
  count: number;
  initialPageSize: number;
  initialSort?: SortColumn[];
  bulkSelectMode?: boolean;
  initialFilters?: Filter[];
  initialFiltersQuery?: Record<string, any>; // DODO added 52010498
  bulkSelectColumnConfig?: {
    id: string;
    Header: (conf: any) => ReactNode;
    Cell: (conf: any) => ReactNode;
  };
  renderCard?: boolean;
  defaultViewMode?: ViewModeType;
}

export function useListViewState({
  fetchData,
  columns,
  data,
  count,
  initialPageSize,
  initialFilters = [],
  initialFiltersQuery = {}, // DODO added 52010498
  initialSort = [],
  bulkSelectMode = false,
  bulkSelectColumnConfig,
  renderCard = false,
  defaultViewMode = 'card',
}: UseListViewConfig) {
  // DODO added 52010498
  // Use ref to store initial filters query to avoid re-renders
  const initialFiltersQueryRef = useRef(initialFiltersQuery);

  const [query, setQuery] = useQueryParams({
    filters: RisonParam,
    pageIndex: NumberParam,
    sortColumn: StringParam,
    sortOrder: StringParam,
    viewMode: StringParam,
  });

  const initialSortBy = useMemo(
    () =>
      query.sortColumn && query.sortOrder
        ? [{ id: query.sortColumn, desc: query.sortOrder === 'desc' }]
        : initialSort,
    [initialSort, query.sortColumn, query.sortOrder],
  );

  const initialState = {
    filters: query.filters
      ? convertFiltersRison(query.filters, initialFilters)
      : [],
    pageIndex: query.pageIndex || 0,
    pageSize: initialPageSize,
    sortBy: initialSortBy,
  };

  const [viewMode, setViewMode] = useState<ViewModeType>(
    (query.viewMode as ViewModeType) ||
      (renderCard ? defaultViewMode : 'table'),
  );

  const columnsWithSelect = useMemo(() => {
    // add exact filter type so filters with falsey values are not filtered out
    const columnsWithFilter = columns.map(f => ({ ...f, filter: 'exact' }));
    return bulkSelectMode
      ? [bulkSelectColumnConfig, ...columnsWithFilter]
      : columnsWithFilter;
  }, [bulkSelectMode, bulkSelectColumnConfig, columns]); // DODO changed 51857488

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    setAllFilters,
    setSortBy,
    selectedFlatRows,
    toggleAllRowsSelected,
    state: { pageIndex, pageSize, sortBy, filters },
  } = useTable(
    {
      columns: columnsWithSelect,
      count,
      data,
      disableFilters: true,
      disableSortRemove: true,
      initialState,
      manualFilters: true,
      manualPagination: true,
      manualSortBy: true,
      autoResetFilters: false,
      pageCount: Math.ceil(count / initialPageSize),
    },
    useFilters,
    useSortBy,
    usePagination,
    useRowState,
    useRowSelect,
  );

  const [internalFilters, setInternalFilters] = useState<InternalFilter[]>(
    () => {
      // DODO changed start 52010498
      if (query.filters && initialFilters.length) {
        return mergeCreateFilterValues(initialFilters, query.filters);
      }
      if (
        initialFilters.length &&
        Object.keys(initialFiltersQueryRef.current).length
      ) {
        return mergeCreateFilterValues(
          initialFilters,
          initialFiltersQueryRef.current,
        );
      }
      // Apply initialFilters directly if no query filters exist
      if (initialFilters.length) {
        return mergeCreateFilterValues(initialFilters, {});
      }
      return [];
    },
    // DODO changed stop 52010498
  );

  useEffect(() => {
    if (initialFilters.length) {
      // DODO added start 52010498
      const newFilters = mergeCreateFilterValues(
        initialFilters,
        query.filters ? query.filters : initialFiltersQueryRef.current,
      );

      // Only update if filters actually changed to prevent unnecessary re-renders
      setInternalFilters(currentFilters => {
        // Skip update if filters are the same
        if (JSON.stringify(currentFilters) === JSON.stringify(newFilters)) {
          return currentFilters;
        }
        return newFilters;
      });
    } else if (initialFilters.length === 0) {
      // Clear filters if initialFilters is empty
      setInternalFilters(currentFilters => {
        if (currentFilters.length === 0) {
          return currentFilters;
        }
        return [];
      });
      // DODO added stop 52010498
    }
  }, [initialFilters]);

  // DODO added 52010498
  // Convert internalFilters to FilterValue[] format for API requests
  const apiFilters = useMemo(
    () => convertFilters(internalFilters),
    [internalFilters],
  );

  // DODO added 52010498
  // Track last fetch config to prevent duplicate requests
  const lastFetchConfigRef = useRef<string>('');

  useEffect(() => {
    // From internalFilters, produce a simplified obj
    const filterObj: Record<string, any> = {};

    internalFilters.forEach(filter => {
      if (
        filter.value !== undefined &&
        (typeof filter.value !== 'string' || filter.value.length > 0)
      ) {
        const currentFilterId = filter.urlDisplay || filter.id;
        filterObj[currentFilterId] = filter.value;
      }
    });

    const queryParams: any = {
      filters: Object.keys(filterObj).length ? filterObj : undefined,
      pageIndex,
    };
    if (sortBy[0]) {
      queryParams.sortColumn = sortBy[0].id;
      queryParams.sortOrder = sortBy[0].desc ? 'desc' : 'asc';
    }

    if (renderCard) {
      queryParams.viewMode = viewMode;
    }

    const method =
      typeof query.pageIndex !== 'undefined' &&
      queryParams.pageIndex !== query.pageIndex
        ? 'push'
        : 'replace';

    setQuery(queryParams, method);

    // DODO added 52010498
    // Create a unique key for this fetch configuration
    const fetchConfigKey = JSON.stringify({
      pageIndex,
      pageSize,
      sortBy,
      filters: apiFilters,
    });

    // DODO changed 52010498
    // Only fetch if configuration actually changed
    if (lastFetchConfigRef.current !== fetchConfigKey) {
      lastFetchConfigRef.current = fetchConfigKey;
      fetchData({ pageIndex, pageSize, sortBy, filters: apiFilters });
    }
  }, [fetchData, pageIndex, pageSize, sortBy, apiFilters]);

  useEffect(() => {
    if (!isEqual(initialState.pageIndex, pageIndex)) {
      gotoPage(initialState.pageIndex);
    }
  }, [query]);

  const applyFilterValue = (index: number, value: any) => {
    setInternalFilters(currentInternalFilters => {
      // skip redundant updates
      if (currentInternalFilters[index].value === value) {
        return currentInternalFilters;
      }

      const update = { ...currentInternalFilters[index], value };
      const updatedFilters = updateInList(
        currentInternalFilters,
        index,
        update,
      );

      setAllFilters(convertFilters(updatedFilters));
      gotoPage(0); // clear pagination on filter
      return updatedFilters;
    });
  };

  return {
    canNextPage,
    canPreviousPage,
    getTableBodyProps,
    getTableProps,
    gotoPage,
    headerGroups,
    pageCount,
    prepareRow,
    rows,
    selectedFlatRows,
    setAllFilters,
    setSortBy,
    state: { pageIndex, pageSize, sortBy, filters, internalFilters, viewMode },
    toggleAllRowsSelected,
    applyFilterValue,
    setViewMode,
    query,
  };
}
