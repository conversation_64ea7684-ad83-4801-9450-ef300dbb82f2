// DODO was here
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SyntheticEvent,
  MutableRefObject,
  forwardRef,
  useRef,
  useEffect,
} from 'react';

import { styled } from '@superset-ui/core';
import Icons from 'src/components/Icons';

export interface IndeterminateCheckboxProps {
  indeterminate: boolean;
  id: string;
  checked: boolean;
  onChange: EventHandler<SyntheticEvent<HTMLInputElement>>;
  title?: string;
  labelText?: string;
  disabled?: boolean; // DODO added 51857488
}

const CheckboxLabel = styled.label<{ disabled?: boolean }>`
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  display: inline-block;
  margin-bottom: 0;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
`;

// DODO added 51857488
const CheckboxHalf = styled(Icons.CheckboxHalf)<{ disabled?: boolean }>`
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.grayscale.light1 : theme.colors.primary.base};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
`;

// DODO added 51857488
const CheckboxOff = styled(Icons.CheckboxOff)<{ disabled?: boolean }>`
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.grayscale.light1 : theme.colors.grayscale.base};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
`;

// DODO added 51857488
const CheckboxOn = styled(Icons.CheckboxOn)<{ disabled?: boolean }>`
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.grayscale.light1 : theme.colors.primary.base};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
`;

const HiddenInput = styled.input`
  &[type='checkbox'] {
    cursor: pointer;
    opacity: 0;
    position: absolute;
    left: 3px;
    margin: 0;
    top: 4px;
  }
`;

const InputContainer = styled.div<{ disabled?: boolean }>`
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  display: inline-block;
  position: relative;
`;

const IndeterminateCheckbox = forwardRef(
  (
    {
      indeterminate,
      id,
      checked,
      onChange,
      title = '',
      labelText = '',
      disabled = false, // DODO added 51857488
    }: IndeterminateCheckboxProps,
    ref: MutableRefObject<any>,
  ) => {
    const defaultRef = useRef<HTMLInputElement>();
    const resolvedRef = ref || defaultRef;

    useEffect(() => {
      resolvedRef.current.indeterminate = indeterminate;
    }, [resolvedRef, indeterminate]);

    return (
      <>
        {/* DODO changed 51857488 */}
        <InputContainer disabled={disabled}>
          {/* DODO changed 51857488 */}
          {indeterminate && <CheckboxHalf disabled={disabled} />}{' '}
          {/* DODO changed 51857488 */}
          {!indeterminate && checked && <CheckboxOn disabled={disabled} />}{' '}
          {/* DODO changed 51857488 */}
          {!indeterminate && !checked && (
            <CheckboxOff disabled={disabled} />
          )}{' '}
          <HiddenInput
            name={id}
            id={id}
            type="checkbox"
            ref={resolvedRef}
            checked={checked}
            onChange={disabled ? () => {} : onChange}
            disabled={disabled} // DODO changed 51857488
          />
        </InputContainer>
        {/* DODO changed 51857488 */}
        <CheckboxLabel title={title} htmlFor={id} disabled={disabled}>
          {labelText}
        </CheckboxLabel>
      </>
    );
  },
);

export default IndeterminateCheckbox;
