import { useState, useMemo } from 'react';
import { Tree, Input, TreeNodeProps } from 'antd-v5';
import { SearchOutlined } from '@ant-design/icons';
import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import {
  Entity,
  EntityType,
  isFolderType,
  TreeNodeData,
} from 'src/DodoExtensions/folders/types';
import FolderTitle from 'src/DodoExtensions/folders/components/FolderTitle';
import DashboardTitle from 'src/DodoExtensions/folders/components/DashboardTitle';
import {
  buildTreeData,
  filterTreeData,
} from 'src/DodoExtensions/folders/utils';

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

const Wrapper = styled.div`
  position: relative;
`;

const StyledInput = styled(Input)`
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  background-color: transparent;
`;

const Content = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4.5}px
    ${({ theme }) => theme.gridUnit * 2}px;
  height: 100%;
  border-right: 1px solid ${({ theme }) => theme.colors.grayscale.light2};

  .tree {
    background-color: transparent;
  }

  .antd5-tree-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 270px;
  }

  .antd5-tree-node-content-wrapper,
  antd5-tree-node-content-wrapper-normal {
    display: flex;
    flex: 1;

    &:hover {
      background-color: transparent;
    }
  }

  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    flex: 1;
  }
`;

const StyledCollapseBtn = styled.button<{ isVisible: boolean }>`
  position: absolute;
  top: 0;
  right: -42px;
  z-index: 110;
  padding-top: ${({ theme }) => theme.gridUnit}px;
  color: ${({ theme, isVisible }) =>
    !isVisible ? theme.colors.primary.base : theme.colors.grayscale.base};
  background: none;
  border: none;
`;

const LeftNavigation = ({
  baseRoute,
  language,
  content,
}: {
  baseRoute: string;
  language: string;
  content: Entity[] | null;
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [searchValue, setSearchValue] = useState('');

  const treeData = useMemo(
    () => buildTreeData(content || [], language, false),
    [content, language],
  );
  const filteredTreeData = filterTreeData(treeData, searchValue);

  return (
    <Wrapper>
      <StyledCollapseBtn
        type="button"
        onClick={() => setIsVisible(prev => !prev)}
        isVisible={isVisible}
      >
        {isVisible && <Icons.Expand />}
        {!isVisible && <Icons.Collapse />}
      </StyledCollapseBtn>

      {isVisible && (
        <Content>
          <StyledInput
            placeholder={t('Search')}
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            allowClear
          />

          <Tree
            treeData={filteredTreeData}
            titleRender={nodeData => {
              const { entity, searchTerm } = nodeData as TreeNodeData;

              const titleContent = isFolderType(entity) ? (
                <FolderTitle
                  editMode={false}
                  folder={entity}
                  searchTerm={searchTerm}
                />
              ) : (
                <DashboardTitle
                  editMode={false}
                  data={entity}
                  searchTerm={searchTerm}
                  showCertification={false}
                  baseRoute={baseRoute}
                  onClick={scrollToTop}
                  highlightOnHover
                />
              );
              return titleContent;
            }}
            switcherIcon={<Icons.CaretDownOutlined />}
            icon={(props: TreeNodeProps & TreeNodeData) => {
              if (props.data.entity.item_type === EntityType.Dashboard) {
                return <Icons.FundViewOutlined iconSize="m" />;
              }
              return props.expanded ? (
                <Icons.FolderOpenOutlined iconSize="m" />
              ) : (
                <Icons.FolderOutlined iconSize="m" />
              );
            }}
            className="tree"
            selectable={false}
            defaultExpandAll
            showLine={{ showLeafIcon: false }}
            showIcon
          />
        </Content>
      )}
    </Wrapper>
  );
};

export default LeftNavigation;
