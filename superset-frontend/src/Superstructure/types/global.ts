export interface RouteConfig {
  idOrSlug: null | string | number;
  location: string;
  name: string;
  nameRU: string;
  hidden: boolean;
}

export interface DashboardConfig {
  idOrSlug: string | number;
  name: string;
  nameRU: string;
  route: string;
}

export interface MicrofrontendNavigation {
  showNavigationMenu: boolean;
  base?: '/OfficeManager/HrMetrics/' | '/OfficeManager/Analytics/';
  routes: RouteConfig[];
  dashboards?: Record<string, DashboardConfig> | null | {};
}

export interface FullConfiguration {
  navigation: MicrofrontendNavigation;
  originUrl: string;
  frontendLogger: boolean;
  basename: string;
}

export interface MicrofrontendParams {
  originUrl?: string;
  businessId?: 'dodopizza' | 'drinkit';
  navigation: MicrofrontendNavigation;
  // -----------
  frontendLogger?: boolean;
  token?: string;
  basename?: string;
}

export type RoutesConfig = MicrofrontendNavigation['routes'];

export interface StylesConfig {
  businessId: string;
  colors: {
    primary: string;
    secondary: string;
    light: string;
    dark: string;
  };
}

export type BusinessId = 'dodopizza' | 'drinkit';

export interface MainComponentProps {
  routes: Array<number | string | null>;
  store: any;
  theme?: any;
  basename: string;
  startDashboardId?: string | number;
}

export interface InitConfig {
  originUrl?: string;
  ENV?: string;
  CREDS?: { username: string; password: string; provider: string };
  FRONTEND_LOGGER?: boolean;
  token?: string;
}

export interface PanelMsgParams {
  title?: string;
  subTitle?: string;
  body?: string;
  extra?: string;
  children?: React.ReactNode;
  stylesConfig: StylesConfig;
}

export interface ErrorParams {
  stackTrace?: string;
  title?: string;
  subTitle?: string;
  body?: string;
  extra?: string;
  children?: React.ReactNode;
}

export interface IPanelMsgObj {
  title: string;
  date?: string;
  subTitle: string;
  extra?: string;
  listTitle?: string;
  listTitleExtra?: string;
  messages?: string[];
  releases?: {
    date: string;
    status: string;
    messages: string[];
  }[];
  messagesExtra?: string[];
  buttons?: { txt: string; link: string }[];
}

export interface RouteFromDashboard {
  hidden: boolean;
  idOrSlug: number;
  name: string;
  nameRU: string;
  location: string;
}

export type ComposedMessage = {
  loaded: boolean;
  name: string;
  errorParams?: string;
};

export type CustomErrorObject<T> = {
  loaded: boolean;
  data: T;
  title: string;
  stackTrace: string;
  errorMsg?: string;
  isCustomError?: boolean;
};

export interface InitializedResponse<T> {
  loaded: boolean;
  error?: string;
  data: T;
  title: string;
  stackTrace: string;
}
