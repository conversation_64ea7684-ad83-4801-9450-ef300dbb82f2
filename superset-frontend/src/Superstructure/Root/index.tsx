/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useMemo, useState } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';

import setupPlugins from 'src/setup/setupPlugins';
import { Version } from 'src/DodoExtensions/components/Version';
import { ThemeProvider } from '@superset-ui/core';
import { theme } from 'src/preamble';
import { AntdThemeProvider } from 'src/components/AntdThemeProvider';
import { useFolder } from 'src/DodoExtensions/folders/hooks/useFolder';
import { FolderSlug } from 'src/DodoExtensions/folders/types';
import setupClient from '../setupClient';
import { GlobalError, Loading, ServiceNotAvailable } from '../components';
import { MicrofrontendNavigation, MicrofrontendParams } from '../types/global';
import { composeAPIConfig } from '../config';

import { store } from '../store';

import LeftNavigation from '../components/LeftNavigation/index';
import Main from '../components/Main/index';

import {
  createFolderContentFromHardcodedDashboards,
  // dirtyHackDodoIs,
  getCsrfToken,
  getLoginToken,
  getRoutes,
} from './utils';

import { MainWrapper, RootWrapper } from './styles';

import { serializeValue } from '../parseEnvFile/utils';
import { addSlash, logConfigs } from './helpers';

import '../../theme';
import { getDefaultDashboard } from '../utils/getDefaultDashboard';

setupClient();
setupPlugins();

function getPageLanguage(): string | null {
  if (!document) {
    return null;
  }
  const select: HTMLSelectElement | null = document.querySelector(
    '#changeLanguage select',
  );
  const selectedLanguage = select ? select.value : null;
  return selectedLanguage;
}

const getLocaleForSuperset = () => {
  const dodoisLanguage = getPageLanguage();
  if (dodoisLanguage) {
    if (dodoisLanguage === 'ru-RU') return 'ru';
    return 'en';
  }
  return 'ru';
};

export const RootComponent = (incomingParams: MicrofrontendParams) => {
  const businessId = incomingParams.businessId || 'dodopizza';
  const hasHardcodedDashboards = Object.keys(
    incomingParams.navigation.dashboards || {},
  ).length;

  const [isLoaded, setLoaded] = useState(false);
  const [isError, setError] = useState(false);
  const [errorObject, setErrorObject] = useState({
    msg: '',
    title: '',
    stackTrace: '',
  });

  const { result: folder, status: folderStatus } = useFolder(
    businessId === 'dodopizza'
      ? FolderSlug.PluginAnalyticsPizza
      : FolderSlug.PluginAnalyticsDrinkit,
    isLoaded && !hasHardcodedDashboards,
  );
  const routes = getRoutes(incomingParams.navigation, folder);

  /**
   * Helper functions
   */
  const handleLoginRequest = async () => {
    const loginResponse = await getLoginToken();

    if (!loginResponse.loaded) {
      setLoaded(false);
      setError(true);

      if (loginResponse.error) {
        setErrorObject({
          msg: loginResponse.error,
          title: loginResponse.title,
          stackTrace: loginResponse.stackTrace,
        });
      } else {
        setErrorObject({
          msg: 'Проверьте, что в Вашей учетной записи Dodo IS заполнены e-mail, имя и фамилия. При отсутствии этих данных, авторизация в сервисе невозможна',
          title: 'UNEXPECTED_ERROR',
          stackTrace: 'UNKNOWN',
        });
      }
      return null;
    }
    return loginResponse;
  };

  const handleCsrfRequest = async ({ useAuth }: { useAuth: boolean }) => {
    const csrfResponse = await getCsrfToken({ useAuth });

    if (!csrfResponse.loaded) {
      setLoaded(false);
      setError(true);

      if (csrfResponse.error) {
        setErrorObject({
          msg: csrfResponse.error,
          title: csrfResponse.title,
          stackTrace: csrfResponse.stackTrace,
        });
      } else {
        setErrorObject({
          msg: 'Проверьте, что в Вашей учетной записи Dodo IS заполнены e-mail, имя и фамилия. При отсутствии этих данных, авторизация в сервисе невозможна',
          title: 'UNEXPECTED_ERROR',
          stackTrace: 'UNKNOWN',
        });
      }
      return null;
    }

    return csrfResponse;
  };

  const params: {
    originUrl: string;
    token: string;
    basename: string;
    frontendLogger: boolean;
    business: string;
    showNavigationMenu: boolean;
    dashboards: MicrofrontendNavigation['dashboards'];
  } = useMemo(() => {
    const env = process.env.WEBPACK_MODE;
    const devStand = process.env.DEV_STAND;

    let parameters = {
      originUrl:
        incomingParams.originUrl || `${window.location.origin}/superset`,
      token: incomingParams.token || '',
      basename: incomingParams.basename
        ? addSlash(incomingParams.basename)
        : '/',
      frontendLogger: incomingParams.frontendLogger || true,
      business: businessId,
      dashboards: incomingParams.navigation.dashboards,
      showNavigationMenu: incomingParams.navigation.showNavigationMenu,
    };

    // Superset API works only with port 3000
    if (env === 'development') {
      parameters = {
        ...parameters,
        basename: '/',
        originUrl: `https://superset-${devStand}.d.yandex.dodois.dev`,
        frontendLogger: true,
      };
    }

    return parameters;
  }, [businessId, incomingParams]);

  useEffect(() => {
    composeAPIConfig(params);
  }, [params]);

  useEffect(() => {
    const initializeLoginAndMenu = async () => {
      let isLoginSkipped = false;
      let useAuth = false;
      let login = null;

      if (process.env.WEBPACK_MODE === 'development') {
        isLoginSkipped = false;
        useAuth = true;
      }
      if (process.env.WEBPACK_MODE === 'production') {
        // On production we do not need to take acceess token
        isLoginSkipped = true;
        // On production we do not have acceess token, so we get csrf without a token
        useAuth = false;
      }

      if (!isLoginSkipped) login = await handleLoginRequest();

      if (
        isLoginSkipped ||
        (!isLoginSkipped && login && login.data && login.data.access_token)
      ) {
        await handleCsrfRequest({ useAuth });
        setLoaded(true);
      }
    };

    initializeLoginAndMenu();
  }, [params]);

  useEffect(() => {
    if (folderStatus === 'error') {
      setLoaded(false);
      setError(true);
      setErrorObject({
        msg: 'Что-то пошло не так c настройкой навигации',
        title: 'UNEXPECTED_ERROR',
        stackTrace: 'UNKNOWN',
      });
    }
  }, [folderStatus]);

  const withNavigation = useMemo(
    () => Boolean(routes?.length) && params.showNavigationMenu,
    [params.showNavigationMenu, routes],
  );

  logConfigs(incomingParams, params);

  const IS_UNAVAILABLE = serializeValue(process.env.isUnavailable) === 'true';

  if (IS_UNAVAILABLE) {
    return <ServiceNotAvailable />;
  }

  if (isError) {
    return (
      <>
        <Version />
        <GlobalError
          title={errorObject.title}
          body={errorObject.msg}
          stackTrace={errorObject.stackTrace}
        />
      </>
    );
  }

  const userLanguage = getLocaleForSuperset();

  const startDashboard = getDefaultDashboard({
    businessId,
    routes: routes || [],
  });

  return (
    <ThemeProvider theme={theme}>
      <AntdThemeProvider>
        <Version />
        <RootWrapper>
          {!routes ? (
            <Loading />
          ) : (
            <Router>
              {withNavigation && (
                <LeftNavigation
                  baseRoute={params.basename}
                  language={userLanguage}
                  content={
                    hasHardcodedDashboards
                      ? createFolderContentFromHardcodedDashboards(
                          incomingParams.navigation,
                        )
                      : folder?.children || []
                  }
                />
              )}
              <MainWrapper withNavigation={withNavigation}>
                <div className="loader-wrapper">
                  <Main
                    routes={routes}
                    store={store}
                    basename={params.basename}
                    startDashboardId={startDashboard}
                  />
                </div>
              </MainWrapper>
            </Router>
          )}
        </RootWrapper>
      </AntdThemeProvider>
    </ThemeProvider>
  );
};
