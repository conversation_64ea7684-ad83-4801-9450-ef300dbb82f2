// DODO was here
// DODO created 54145210
import { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { t, styled } from '@superset-ui/core';
import { Input } from 'src/components/Input';
import Table, { TableSize } from 'src/components/Table';
import { RootState, DashboardLayout, LayoutItem } from 'src/dashboard/types';
import { CHART_TYPE } from 'src/dashboard/util/componentTypes';
import CopyToClipboard from 'src/components/CopyToClipboard';

interface ChartInfo {
  sliceId: number;
  sliceName: string;
  sliceNameOverride?: string;
  sliceNameOverrideRU?: string;
}

const StyledContainer = styled.div`
  ${({ theme }) => `
    .chart-list-search {
      margin-bottom: ${theme.gridUnit * 4}px;
    }

    .ant-table-tbody {
      overflow-y: auto;
      max-height: calc(100vh - 300px);
    }
    .ant-table-tbody > tr > td {
      padding: ${theme.gridUnit * 2}px ${theme.gridUnit * 3}px;
    }

    .slice-id {
      font-weight: ${theme.typography.weights.bold};
      color: ${theme.colors.primary.base};
      user-select: text;
    }

    .slice-override {
      color: ${theme.colors.grayscale.base};
      font-style: italic;
    }
  `}
`;

const columns = [
  {
    title: t('ID'),
    dataIndex: 'sliceId',
    key: 'sliceId',
    width: 80,
    render: (sliceId: number) => (
      <CopyToClipboard
        copyNode={<span className="slice-id">{sliceId}</span>}
        text={`.dashboard-chart-id-${sliceId}`}
        tooltipText={t('Copy selector')}
        shouldShowText={false}
        wrapped={false}
      />
    ),
  },
  {
    title: t('Original title'),
    dataIndex: 'sliceName',
    key: 'sliceName',
  },
  {
    title: t('Title (EN)'),
    dataIndex: 'sliceNameOverride',
    key: 'sliceNameOverride',
    render: (override: string) => (
      <span className="slice-override">{override || '-'}</span>
    ),
  },
  {
    title: t('Title (RU)'),
    dataIndex: 'sliceNameOverrideRU',
    key: 'sliceNameOverrideRU',
    render: (override: string) => (
      <span className="slice-override">{override || '-'}</span>
    ),
  },
];

const ChartList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const dashboardLayout = useSelector<RootState, DashboardLayout>(
    state => state.dashboardLayout.present,
  );

  const sliceEntities = useSelector<RootState, any>(
    state => state.sliceEntities.slices,
  );

  const chartInfos = useMemo(() => {
    const charts: ChartInfo[] = [];

    Object.values(dashboardLayout).forEach((component: LayoutItem) => {
      if (component.type === CHART_TYPE && component.meta?.chartId) {
        const sliceId = component.meta.chartId;
        const slice = sliceEntities[sliceId];

        if (slice) {
          const { sliceNameOverride, sliceNameOverrideRU } = component.meta;

          charts.push({
            sliceId,
            sliceName: slice.slice_name || '',
            sliceNameOverride,
            sliceNameOverrideRU,
          });
        }
      }
    });

    return charts.sort((a, b) => a.sliceId - b.sliceId);
  }, [dashboardLayout, sliceEntities]);

  const filteredCharts = useMemo(() => {
    if (!searchTerm) return chartInfos;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return chartInfos.filter(
      chart =>
        chart.sliceName.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceNameOverride?.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceNameOverrideRU?.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceId.toString().includes(lowerSearchTerm),
    );
  }, [chartInfos, searchTerm]);

  return (
    <StyledContainer>
      <Input
        className="chart-list-search"
        placeholder={t('Search charts by name or ID')}
        value={searchTerm}
        onChange={e => setSearchTerm(e.target.value)}
        allowClear
      />

      <Table
        columns={columns}
        data={filteredCharts}
        size={TableSize.Small}
        usePagination
        defaultPageSize={10}
        pageSizeOptions={[]}
      />
    </StyledContainer>
  );
};

export default ChartList;
