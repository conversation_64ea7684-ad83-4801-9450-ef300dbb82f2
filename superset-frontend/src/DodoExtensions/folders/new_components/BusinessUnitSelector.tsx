import { Button, Typography } from 'antd';

const { Title, Text } = Typography;

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
  folders: any[];
}

interface BusinessUnitSelectorProps {
  businessData: BusinessUnit[];
  selectedBusiness: string;
  onBusinessChange: (businessId: string) => void;
  totalDashboards: number;
  currentBusiness?: BusinessUnit;
}

const BusinessUnitSelector = ({
  businessData,
  selectedBusiness,
  onBusinessChange,
  totalDashboards,
  currentBusiness,
}: BusinessUnitSelectorProps) => (
  <div
    style={{
      padding: '16px 16px 12px 16px',
      borderBottom: '1px solid #e9ecef',
      backgroundColor: '#f8f9fa',
    }}
  >
    <Title level={4} style={{ margin: '0 0 12px 0', fontSize: '16px' }}>
      Business Units
    </Title>
    <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
      {businessData.map(business => (
        <Button
          key={business.id}
          size="small"
          type={selectedBusiness === business.id ? 'primary' : 'default'}
          onClick={() => onBusinessChange(business.id)}
          style={{
            backgroundColor:
              selectedBusiness === business.id ? business.color : 'transparent',
            borderColor: business.color,
            color: selectedBusiness === business.id ? '#fff' : business.color,
            fontWeight: 500,
            fontSize: '12px',
          }}
        >
          {business.name}
        </Button>
      ))}
    </div>
    <Text type="secondary" style={{ fontSize: '12px' }}>
      {totalDashboards} dashboards in {currentBusiness?.name}
    </Text>
  </div>
);

export default BusinessUnitSelector;
