import {
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  StarOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Entity, EntityType, Folder, Dashboard } from '../types';

interface DashboardItem {
  id: string;
  name: string;
  description: string;
  lastUpdated: string;
  status: 'Published' | 'Draft' | 'Under Review';
  type:
    | 'Product Analytics'
    | 'Operations'
    | 'Marketing'
    | 'Finance'
    | 'Business Intelligence'
    | 'Customer Analytics'
    | 'Performance Metrics'
    | 'Technical';
  isFavorite: boolean;
  isCertified: boolean;
  owners: string[];
  collaborators: string[];
  tags?: string[];
}

interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
  icon: React.ComponentType<any>;
  tags: string[];
  category: string;
  dashboards: DashboardItem[];
  subfolders?: FolderData[];
  isExpanded?: boolean;
}

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
  folders: FolderData[];
}

// Data transformation utilities for integration with existing system
export const transformEntityToDashboardItem = (
  entity: Entity,
): DashboardItem | null => {
  if (entity.item_type !== EntityType.Dashboard) return null;

  const dashboard = entity as Dashboard;
  return {
    id: dashboard.id.toString(),
    name: dashboard.name_en || dashboard.name_ru || 'Untitled Dashboard',
    description: `Dashboard ID: ${dashboard.id}`,
    lastUpdated: 'Recently updated',
    status: 'Published' as const,
    type: 'Business Intelligence',
    isFavorite: false,
    isCertified: Boolean(dashboard.certified_by),
    owners: dashboard.certified_by ? [dashboard.certified_by] : [],
    collaborators: [],
    tags: ['Dashboard'],
  };
};

export const transformFolderToFolderData = (folder: Folder): FolderData => {
  const dashboards =
    (folder.children
      ?.filter(child => child.item_type === EntityType.Dashboard)
      .map(transformEntityToDashboardItem)
      .filter(Boolean) as DashboardItem[]) || [];

  const subfolders =
    folder.children
      ?.filter(child => child.item_type === EntityType.Folder)
      .map(child => transformFolderToFolderData(child as Folder)) || [];

  return {
    id: folder.id.toString(),
    title: folder.name_en || folder.name_ru || 'Untitled Folder',
    subtitle:
      folder.description_en || folder.description_ru || 'No description',
    dashboardCount:
      dashboards.length +
      subfolders.reduce((sum, sf) => sum + sf.dashboardCount, 0),
    icon: BarChartOutlined, // Default icon
    tags: ['Folder'],
    category: 'general',
    dashboards,
    subfolders: subfolders.length > 0 ? subfolders : undefined,
    isExpanded: false,
  };
};

export const createBusinessUnitFromFolders = (
  globalFolder: Folder | null,
  // pluginFolder: Folder | null,
  // personalFolder: Folder | null,
  isAdmin: boolean,
): BusinessUnit[] => {
  const folders: FolderData[] = [];

  if (globalFolder) {
    folders.push({
      ...transformFolderToFolderData(globalFolder),
      title: 'Global folder',
      subtitle: 'Shared dashboards and folders',
      category: 'global',
      icon: StarOutlined,
    });
  }

  // if (isAdmin && pluginFolder) {
  //   folders.push({
  //     ...transformFolderToFolderData(pluginFolder),
  //     title: 'Office manager folder',
  //     subtitle: 'Administrative dashboards',
  //     category: 'admin',
  //     icon: SettingOutlined,
  //   });
  // }

  // if (personalFolder) {
  //   folders.push({
  //     ...transformFolderToFolderData(personalFolder),
  //     title: 'Personal folder',
  //     subtitle: 'Your personal dashboards',
  //     category: 'personal',
  //     icon: TeamOutlined,
  //   });
  // }

  return [
    {
      id: 'current',
      name: 'Current Workspace',
      color: 'rgb(255, 105, 0)',
      folders,
    },
  ];
};
