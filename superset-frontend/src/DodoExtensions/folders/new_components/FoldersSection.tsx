import { Card, Typography, Badge } from 'antd';
import { FolderOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
}

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
}

interface FoldersSectionProps {
  subfolders: FolderData[];
  currentBusiness?: BusinessUnit;
  onFolderClick: (folderId: string) => void;
}

const FoldersSection = ({
  subfolders,
  currentBusiness,
  onFolderClick,
}: FoldersSectionProps) => {
  if (subfolders.length === 0) {
    return null;
  }

  return (
    <div style={{ marginBottom: '32px' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '16px',
          paddingBottom: '8px',
          borderBottom: '2px solid #e9ecef',
        }}
      >
        <FolderOutlined
          style={{
            fontSize: '16px',
            color: currentBusiness?.color,
            marginRight: '8px',
          }}
        />
        <Title
          level={4}
          style={{
            margin: 0,
            fontSize: '16px',
            color: '#212529',
            fontWeight: 600,
          }}
        >
          Folders ({subfolders.length})
        </Title>
      </div>
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '16px',
          alignItems: 'flex-start',
        }}
      >
        {subfolders.map(subfolder => (
          <Card
            key={subfolder.id}
            size="small"
            style={{
              borderRadius: '4px',
              border: '1px solid #dee2e6',
              backgroundColor: '#fafbfc',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              transform: 'translateY(0)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              minWidth: 'fit-content',
              maxWidth: '280px',
              flex: '0 0 auto',
            }}
            bodyStyle={{
              padding: '16px',
              display: 'flex',
              flexDirection: 'column',
              minWidth: 'fit-content',
            }}
            onClick={() => onFolderClick(subfolder.id)}
            onMouseEnter={e => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
              e.currentTarget.style.borderColor =
                currentBusiness?.color || '#1890ff';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
              e.currentTarget.style.borderColor = '#dee2e6';
            }}
          >
            <div style={{ flex: 1 }}>
              {/* Header with folder icon, title and badge */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flex: 1,
                    marginRight: '8px',
                    minWidth: 'fit-content',
                  }}
                >
                  <FolderOutlined
                    style={{
                      fontSize: '16px',
                      color: currentBusiness?.color || '#1890ff',
                      marginRight: '8px',
                      flexShrink: 0,
                    }}
                  />
                  <div
                    style={{
                      fontSize: '14px',
                      fontWeight: 600,
                      color: '#212529',
                      lineHeight: '1.3',
                      whiteSpace: 'nowrap',
                      minWidth: 'fit-content',
                    }}
                  >
                    {subfolder.title}
                  </div>
                </div>
                <Badge
                  count={subfolder.dashboardCount}
                  size="default"
                  style={{
                    backgroundColor: currentBusiness?.color,
                    fontSize: '10px',
                  }}
                />
              </div>

              {/* Description */}
              <div
                style={{
                  fontSize: '12px',
                  color: '#6c757d',
                  lineHeight: '1.4',
                }}
              >
                {subfolder.subtitle}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default FoldersSection;
