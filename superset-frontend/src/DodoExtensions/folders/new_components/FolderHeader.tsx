import { Typography } from 'antd';

const { Title, Text } = Typography;

interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
}

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
}

interface FolderHeaderProps {
  currentFolder?: FolderData | null;
  currentBusiness?: BusinessUnit;
}

const FolderHeader = ({
  currentFolder,
  currentBusiness,
}: FolderHeaderProps) => (
  <div
    style={{
      padding: '16px 24px',
      backgroundColor: 'rgb(255, 255, 255)',
      borderBottom: '1px solid rgb(233, 236, 239)',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    }}
  >
    {/* Left side - Folder info */}
    <div>
      <div
        style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}
      >
        <Title level={3} style={{ margin: 0, fontSize: '20px' }}>
          {currentFolder?.title || 'Select a folder'}
        </Title>
      </div>
      <Text type="secondary" style={{ fontSize: '14px' }}>
        {currentFolder
          ? `${currentFolder.subtitle} • ${currentFolder.dashboardCount} dashboards`
          : `Choose a folder from ${currentBusiness?.name} to view dashboards`}
      </Text>
    </div>
  </div>
);

export default FolderHeader;
