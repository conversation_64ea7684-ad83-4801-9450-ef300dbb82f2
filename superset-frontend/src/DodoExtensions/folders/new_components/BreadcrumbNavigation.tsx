import { RightOutlined } from '@ant-design/icons';

interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
  subfolders?: FolderData[];
}

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
}

interface BreadcrumbNavigationProps {
  currentBusiness?: BusinessUnit;
  selectedFolder: string;
  currentFolders: FolderData[];
  onBreadcrumbClick: (folderId?: string) => void;
  buildBreadcrumbPath: (
    folderId: string,
    folders: FolderData[],
  ) => FolderData[];
}

const BreadcrumbNavigation = ({
  currentBusiness,
  selectedFolder,
  currentFolders,
  onBreadcrumbClick,
  buildBreadcrumbPath,
}: BreadcrumbNavigationProps) => (
  <div
    style={{
      padding: '12px 24px',
      backgroundColor: '#f8f9fa',
      borderBottom: '1px solid #e9ecef',
      fontSize: '12px',
      color: '#6c757d',
    }}
  >
    <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
      {/* Business Unit - clickable */}
      <span
        style={{
          color: currentBusiness?.color,
          cursor: 'pointer',
          fontWeight: !selectedFolder ? 600 : 400,
          textDecoration: 'none',
        }}
        onClick={() => onBreadcrumbClick()}
        onMouseEnter={e => {
          e.currentTarget.style.textDecoration = 'underline';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.textDecoration = 'none';
        }}
      >
        {currentBusiness?.name}
      </span>

      {selectedFolder &&
        (() => {
          const breadcrumbPath = buildBreadcrumbPath(
            selectedFolder,
            currentFolders,
          );
          return breadcrumbPath.map((folder, index) => (
            <span key={folder.id}>
              <RightOutlined style={{ fontSize: '8px', margin: '0 4px' }} />
              <span
                style={{
                  color:
                    index === breadcrumbPath.length - 1 ? '#212529' : '#6c757d',
                  fontWeight: index === breadcrumbPath.length - 1 ? 600 : 400,
                  cursor:
                    index === breadcrumbPath.length - 1 ? 'default' : 'pointer',
                  textDecoration: 'none',
                }}
                onClick={() =>
                  index !== breadcrumbPath.length - 1
                    ? onBreadcrumbClick(folder.id)
                    : undefined
                }
                onMouseEnter={e => {
                  if (index !== breadcrumbPath.length - 1) {
                    e.currentTarget.style.textDecoration = 'underline';
                  }
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.textDecoration = 'none';
                }}
              >
                {folder.title}
              </span>
            </span>
          ));
        })()}
    </div>
  </div>
);

export default BreadcrumbNavigation;
