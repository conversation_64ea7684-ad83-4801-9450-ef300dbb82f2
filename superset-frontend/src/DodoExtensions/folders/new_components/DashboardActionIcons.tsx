import React from 'react';

interface DashboardItem {
  id: string;
  name: string;
  description: string;
  lastUpdated: string;
  status?: 'Published' | 'Draft' | 'Under Review';
  type?: string;
  isFavorite?: boolean;
  isCertified?: boolean;
  collaborators?: string[];
  tags?: string[];
}

interface DashboardActionIconsProps {
  dashboard: DashboardItem;
  onDeleteDashboard?: (dashboard: DashboardItem) => void;
  onUploadDashboard?: (dashboard: DashboardItem) => void;
  onEditDashboard?: (dashboard: DashboardItem) => void;
}

const DashboardActionIcons: React.FC<DashboardActionIconsProps> = ({
  dashboard,
  onDeleteDashboard,
  onUploadDashboard,
  onEditDashboard,
}) => (
  <div
    style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: '8px',
    }}
  >
    <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
      {/* Delete icon */}
      <div
        style={{
          width: '20px',
          height: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          borderRadius: '3px',
          transition: 'background-color 0.2s',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        onClick={e => {
          e.stopPropagation();
          onDeleteDashboard?.(dashboard);
        }}
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#666"
          strokeWidth="2"
        >
          <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14zM10 11v6M14 11v6" />
        </svg>
      </div>

      {/* Upload icon */}
      <div
        style={{
          width: '20px',
          height: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          borderRadius: '3px',
          transition: 'background-color 0.2s',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        onClick={e => {
          e.stopPropagation();
          onUploadDashboard?.(dashboard);
        }}
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#666"
          strokeWidth="2"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12" />
        </svg>
      </div>

      {/* Edit icon */}
      <div
        style={{
          width: '20px',
          height: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          borderRadius: '3px',
          transition: 'background-color 0.2s',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        onClick={e => {
          e.stopPropagation();
          onEditDashboard?.(dashboard);
        }}
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#ff6900"
          strokeWidth="2"
        >
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
        </svg>
      </div>
    </div>
  </div>
);

export default DashboardActionIcons;
