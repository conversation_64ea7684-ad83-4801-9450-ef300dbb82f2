import { useState, useEffect, Fragment } from 'react';
import { Typo<PERSON>, Badge } from 'antd';
import {
  FolderOutlined,
  CaretRightOutlined,
  CaretDownOutlined,
} from '@ant-design/icons';
import { styled } from '@superset-ui/core';
import BusinessUnitSelector from './BusinessUnitSelector';
import FolderHeader from './FolderHeader';
import BreadcrumbNavigation from './BreadcrumbNavigation';
import FoldersSection from './FoldersSection';
import DashboardsSection from './DashboardsSection';
import Folder from './Folder';

const { Title, Text } = Typography;

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
  folders: any[];
}

interface DashboardFoldersProps {
  businessData?: BusinessUnit[];
  onDashboardClick?: (dashboard: any) => void;
  onFavoriteToggle?: (dashboard: any) => void;
  onDeleteDashboard?: (dashboard: any) => void;
  onUploadDashboard?: (dashboard: any) => void;
  onEditDashboard?: (dashboard: any) => void;
}

const DashboardFolders = ({
  businessData,
  onDashboardClick,
  onFavoriteToggle,
  onDeleteDashboard,
  onUploadDashboard,
  onEditDashboard,
}: DashboardFoldersProps = {}) => {
  // Use external data if provided, otherwise fall back to static data
  const activeBusinessData = businessData || [];

  const [selectedBusiness, setSelectedBusiness] = useState<string>(
    activeBusinessData[0]?.id || 'pizza',
  );
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(),
  );

  const currentBusiness =
    activeBusinessData.find(b => b.id === selectedBusiness) ||
    activeBusinessData[0];
  const currentFolders = currentBusiness?.folders || [];

  // Helper function to find a folder by ID (including subfolders)
  const findFolderById = (folderId: string, folders: any[]): any => {
    for (const folder of folders) {
      if (folder.id === folderId) {
        return folder;
      }
      if (folder.subfolders) {
        const found = findFolderById(folderId, folder.subfolders);
        if (found) return found;
      }
    }
    return null;
  };

  // Helper function to toggle folder expansion
  const toggleFolderExpansion = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  // Helper function to build breadcrumb path starting with business unit
  const buildBreadcrumbPath = (folderId: string, folders: any[]): any[] => {
    for (const folder of folders) {
      if (folder.id === folderId) {
        return [folder];
      }
      if (folder.subfolders) {
        const subPath = buildBreadcrumbPath(folderId, folder.subfolders);
        if (subPath.length > 0) {
          return [folder, ...subPath];
        }
      }
    }
    return [];
  };

  // Handle breadcrumb navigation
  const handleBreadcrumbClick = (folderId?: string) => {
    if (folderId) {
      setSelectedFolder(folderId);
    } else {
      // Clicked on business unit - go back to business unit view
      setSelectedFolder('');
    }
  };

  // Set default folder when business changes
  useEffect(() => {
    if (currentFolders.length > 0 && !selectedFolder) {
      setSelectedFolder(currentFolders[0].id);
    }
  }, [selectedBusiness, currentFolders, selectedFolder]);

  const totalDashboards = currentFolders.reduce(
    (sum, folder) => sum + folder.dashboardCount,
    0,
  );
  const currentFolder = findFolderById(selectedFolder, currentFolders);

  const dashboards = currentFolder?.dashboards || [];
  const subfolders = currentFolder?.subfolders || [];

  // Recursive function to render folders and subfolders (max depth: 5)
  const renderFolder = (folder: any, level = 0) => {
    // Prevent rendering beyond maximum depth of 5 levels
    if (level >= 5) {
      return null;
    }

    const isSelected = selectedFolder === folder.id;
    const businessColor = currentBusiness?.color || 'rgb(255, 105, 0)';
    const lightBusinessColor =
      currentBusiness?.id === 'pizza' ? '#fff2e6' : '#e8f0ff';
    const isExpanded = expandedFolders.has(folder.id);
    const hasSubfolders =
      folder.subfolders && folder.subfolders.length > 0 && level < 4; // Only show expand if not at max depth
    const indentLevel = level * 16;

    return (
      <Fragment key={folder.id}>
        {/* Main folder item */}
        <Folder
          folderId={folder.id}
          folderTitle={folder.title}
          folderSubtitle={folder.subtitle}
          folderDashboardCount={folder.dashboardCount}
          isSelected={isSelected}
          businessColor={businessColor}
          lightBusinessColor={lightBusinessColor}
          indentLevel={indentLevel}
          hasSubfolders={hasSubfolders}
          isExpanded={isExpanded}
          toggleFolderExpansion={toggleFolderExpansion}
          setSelectedFolder={setSelectedFolder}
        />

        {/* Render subfolders if expanded */}
        {hasSubfolders && isExpanded && (
          <div>
            {folder.subfolders.map((subfolder: any) =>
              renderFolder(subfolder, level + 1),
            )}
          </div>
        )}
      </Fragment>
    );
  };

  return (
    <div
      style={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#f8f9fa',
      }}
    >
      <style>
        {`
          .dashboard-title:hover {
            text-decoration: underline !important;
          }
        `}
      </style>

      {/* Main Content Area */}
      <div style={{ flex: 1, display: 'flex', backgroundColor: '#f8f9fa' }}>
        {/* Sidebar - Business & Folder Navigation */}
        <div
          style={{
            width: '280px',
            backgroundColor: '#fff',
            borderRight: '1px solid #e9ecef',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* Business Unit Selector */}
          <BusinessUnitSelector
            businessData={activeBusinessData}
            selectedBusiness={selectedBusiness}
            onBusinessChange={(businessId: string) => {
              setSelectedBusiness(businessId);
              setSelectedFolder('');
            }}
            totalDashboards={totalDashboards}
            currentBusiness={currentBusiness}
          />

          {/* Folder Categories Header */}
          <div
            style={{
              padding: '12px 16px 8px 16px',
              borderBottom: '1px solid #e9ecef',
              backgroundColor: '#f8f9fa',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FolderOutlined style={{ fontSize: '12px', color: '#6c757d' }} />
              <Text
                style={{ fontSize: '13px', fontWeight: 600, color: '#6c757d' }}
              >
                FOLDERS & DASHBOARDS
              </Text>
            </div>
          </div>

          {/* Folder List */}
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {currentFolders.map(folder => renderFolder(folder, 0))}
          </div>
        </div>

        {/* Main Content - Dashboard Details */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Main Header */}
          <FolderHeader
            currentFolder={currentFolder}
            currentBusiness={currentBusiness}
          />

          {/* Breadcrumb Navigation */}
          <BreadcrumbNavigation
            currentBusiness={currentBusiness}
            selectedFolder={selectedFolder}
            currentFolders={currentFolders}
            onBreadcrumbClick={handleBreadcrumbClick}
            buildBreadcrumbPath={buildBreadcrumbPath}
          />

          {/* Dashboard Grid */}
          <div
            style={{
              flex: 1,
              padding: '24px',
              overflowY: 'auto',
              backgroundColor: '#f8f9fa',
            }}
          >
            {!currentFolder ? (
              // Empty state when no folder is selected
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '400px',
                  textAlign: 'center',
                }}
              >
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: '16px',
                    border: '2px dashed #dee2e6',
                  }}
                >
                  <FolderOutlined
                    style={{ fontSize: '32px', color: '#adb5bd' }}
                  />
                </div>
                <Title
                  level={4}
                  style={{ color: '#6c757d', marginBottom: '8px' }}
                >
                  Select a folder to view dashboards
                </Title>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  Choose a folder from the left panel to see its dashboards and
                  analytics
                </Text>
              </div>
            ) : (
              <div>
                {/* Folders Section */}
                <FoldersSection
                  subfolders={subfolders}
                  currentBusiness={currentBusiness}
                  onFolderClick={setSelectedFolder}
                />

                {/* Dashboards Section */}
                <DashboardsSection
                  dashboards={dashboards}
                  currentBusiness={currentBusiness}
                  onDashboardClick={
                    onDashboardClick ||
                    (dashboard => {
                      console.log('Dashboard clicked:', dashboard.name);
                    })
                  }
                  onFavoriteToggle={
                    onFavoriteToggle ||
                    (dashboard => {
                      console.log('Toggle favorite:', dashboard.name);
                    })
                  }
                  onDeleteDashboard={
                    onDeleteDashboard ||
                    (dashboard => {
                      console.log('Delete dashboard:', dashboard.name);
                    })
                  }
                  onUploadDashboard={
                    onUploadDashboard ||
                    (dashboard => {
                      console.log('Upload dashboard:', dashboard.name);
                    })
                  }
                  onEditDashboard={
                    onEditDashboard ||
                    (dashboard => {
                      console.log('Edit dashboard:', dashboard.name);
                    })
                  }
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardFolders;
