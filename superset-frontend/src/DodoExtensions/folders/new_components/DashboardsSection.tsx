import { Card, Row, Col, Typography, Space, Tag, Switch, Table } from 'antd';
import {
  DashboardOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { useState } from 'react';
import DashboardActionIcons from './DashboardActionIcons';

const { Title } = Typography;

interface DashboardItem {
  id: string;
  name: string;
  description: string;
  lastUpdated: string;
  status?: 'Published' | 'Draft' | 'Under Review';
  type?: string;
  isFavorite?: boolean;
  isCertified?: boolean;
  collaborators?: string[];
  tags?: string[];
}

interface BusinessUnit {
  id: string;
  name: string;
  color: string;
}

interface DashboardsSectionProps {
  dashboards: DashboardItem[];
  currentBusiness?: BusinessUnit;
  onDashboardClick?: (dashboard: DashboardItem) => void;
  onFavoriteToggle?: (dashboard: DashboardItem) => void;
  onDeleteDashboard?: (dashboard: DashboardItem) => void;
  onUploadDashboard?: (dashboard: DashboardItem) => void;
  onEditDashboard?: (dashboard: DashboardItem) => void;
}

const DashboardsSection = ({
  dashboards,
  currentBusiness,
  onDashboardClick,
  onFavoriteToggle,
  onDeleteDashboard,
  onUploadDashboard,
  onEditDashboard,
}: DashboardsSectionProps) => {
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');

  if (dashboards.length === 0) {
    return null;
  }

  const renderTableView = () => {
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: DashboardItem, index: number) => {
          const isCertified = record.isCertified ?? index % 3 === 0;
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {isCertified && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    color: currentBusiness?.color || '#ff6900',
                    flexShrink: 0,
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    style={{ display: 'block' }}
                  >
                    <path
                      fill="currentColor"
                      d="m23 12-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69zM9.38 16.01 7 13.61a.996.996 0 0 1 0-1.41l.07-.07c.39-.39 1.03-.39 1.42 0l1.61 1.62 5.15-5.16c.39-.39 1.03-.39 1.42 0l.07.07c.39.39.39 1.02 0 1.41l-5.92 5.94c-.41.39-1.04.39-1.44 0"
                    />
                  </svg>
                </div>
              )}
              <span
                style={{
                  cursor: 'pointer',
                  color: '#1890ff',
                  fontWeight: 500,
                }}
                onClick={() => onDashboardClick?.(record)}
              >
                {text}
              </span>
            </div>
          );
        },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string, _: DashboardItem, index: number) => {
          const actualStatus =
            status ?? (index % 5 === 0 ? 'Draft' : 'Published');
          return (
            <span
              style={{
                transition: 'background-color 0.3s',
                whiteSpace: 'nowrap',
                cursor: 'default',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                backgroundColor: 'rgb(240, 240, 240)',
                borderColor: 'transparent',
                borderRadius: '21px',
                padding: '0.35em 0.8em',
                lineHeight: 1,
                color: 'rgb(23, 23, 23)',
                fontSize: '12px',
                display: 'inline-block',
                border: '1px solid transparent',
              }}
            >
              {actualStatus.toLowerCase()}
            </span>
          );
        },
      },
      {
        title: 'Tags',
        dataIndex: 'tags',
        key: 'tags',
        render: (tags: string[], _: DashboardItem, index: number) => {
          const actualTags =
            tags ||
            (index % 2 === 0 ? ['Analytics', 'Dashboard'] : ['KPI', 'Metrics']);
          return (
            <Space size="small">
              {actualTags.slice(0, 3).map((tag: string, tagIndex: number) => (
                <Tag
                  key={tagIndex}
                  style={{
                    margin: 0,
                    background: '#F7F7F7',
                    border: '1px solid #E0E0E0',
                  }}
                >
                  {tag}
                </Tag>
              ))}
            </Space>
          );
        },
      },
      {
        title: 'Last Updated',
        dataIndex: 'lastUpdated',
        key: 'lastUpdated',
        render: (text: string) => (
          <span style={{ fontSize: '12px', color: '#666' }}>{text}</span>
        ),
      },
      {
        title: 'Favorite',
        key: 'favorite',
        render: (record: DashboardItem, _: any, index: number) => {
          const isFavorite = record.isFavorite ?? index % 4 === 0;
          return (
            <span
              role="img"
              aria-label={
                isFavorite ? 'favorite-selected' : 'favorite-unselected'
              }
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={e => {
                e.stopPropagation();
                onFavoriteToggle?.(record);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="currentColor"
                aria-hidden="true"
                focusable="false"
                viewBox="0 0 24 24"
                style={{ color: isFavorite ? 'rgb(251, 199, 0)' : '#B2B2B2' }}
              >
                <path
                  fill="currentColor"
                  fillRule="evenodd"
                  d="M19.973 9.674a.8.8 0 0 0-.687-.525l-4.546-.651-2.037-4.056A.8.8 0 0 0 11.984 4a.8.8 0 0 0-.719.442L9.228 8.49l-4.546.659a.8.8 0 0 0-.647.533.77.77 0 0 0 .2.784l3.3 3.138-.8 4.455c-.058.3.067.606.32.784a.81.81 0 0 0 .839.055l4.09-2.094 4.074 2.102a.75.75 0 0 0 .368.094c.17 0 .334-.052.471-.149a.78.78 0 0 0 .32-.784l-.799-4.456 3.3-3.137a.78.78 0 0 0 .255-.8m-5.714 3.23.558 3.111-1.916-.989-.913-.47-2.855 1.46.37-2.06.19-1.06-2.239-2.128 2.06-.299 1.032-.15 1.437-2.855.97 1.931.47.934 3.08.441z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          );
        },
      },
      {
        title: 'Actions',
        key: 'actions',
        render: (record: DashboardItem) => (
          <DashboardActionIcons
            dashboard={record}
            onDeleteDashboard={onDeleteDashboard}
            onUploadDashboard={onUploadDashboard}
            onEditDashboard={onEditDashboard}
          />
        ),
      },
    ];

    return (
      <Table
        dataSource={dashboards}
        columns={columns}
        rowKey="id"
        pagination={false}
        size="small"
        style={{ marginTop: '8px' }}
        onRow={record => ({
          style: { cursor: 'pointer' },
          onClick: () => onDashboardClick?.(record),
        })}
      />
    );
  };

  return (
    <div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '16px',
          paddingBottom: '8px',
          borderBottom: '2px solid #e9ecef',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DashboardOutlined
            style={{
              fontSize: '16px',
              color: currentBusiness?.color,
              marginRight: '8px',
            }}
          />
          <Title
            level={4}
            style={{
              margin: 0,
              fontSize: '16px',
              color: '#212529',
              fontWeight: 600,
            }}
          >
            Dashboards ({dashboards.length})
          </Title>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <AppstoreOutlined
            style={{
              color:
                viewMode === 'cards'
                  ? currentBusiness?.color || '#1890ff'
                  : '#8c8c8c',
              fontSize: '16px',
            }}
          />
          <Switch
            checked={viewMode === 'table'}
            onChange={checked => setViewMode(checked ? 'table' : 'cards')}
            size="small"
          />
          <UnorderedListOutlined
            style={{
              color:
                viewMode === 'table'
                  ? currentBusiness?.color || '#1890ff'
                  : '#8c8c8c',
              fontSize: '16px',
            }}
          />
        </div>
      </div>
      {viewMode === 'cards' ? (
        <Row gutter={[16, 16]}>
          {dashboards.map((dashboard, index) => {
            // Use dashboard properties from constants or generate fallback data
            const isCertified = dashboard.isCertified ?? index % 3 === 0;
            const isFavorite = dashboard.isFavorite ?? index % 4 === 0;
            const status =
              dashboard.status ?? (index % 5 === 0 ? 'Draft' : 'Published');
            const tags =
              dashboard.tags ||
              (index % 2 === 0
                ? ['Analytics', 'Dashboard']
                : ['KPI', 'Metrics']);

            return (
              <Col xs={24} sm={12} md={8} lg={6} key={dashboard.id}>
                <Card
                  size="small"
                  style={{
                    borderRadius: '4px',
                    border: '1px solid #dee2e6',
                  }}
                  bodyStyle={{
                    padding: '12px',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <div style={{ flex: 1 }}>
                    {/* Header with title */}
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        justifyContent: 'space-between',
                        marginBottom: '6px',
                      }}
                    >
                      {/* Left side: Certified icon + Title */}
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          flex: 1,
                          marginRight: '8px',
                          minWidth: 0,
                        }}
                      >
                        {/* Certified icon */}
                        {isCertified && (
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '16px',
                              height: '16px',
                              color: currentBusiness?.color || '#ff6900',
                              flexShrink: 0,
                              marginRight: '6px',
                              marginTop: '1px',
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              style={{ display: 'block' }}
                            >
                              <path
                                fill="currentColor"
                                d="m23 12-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69zM9.38 16.01 7 13.61a.996.996 0 0 1 0-1.41l.07-.07c.39-.39 1.03-.39 1.42 0l1.61 1.62 5.15-5.16c.39-.39 1.03-.39 1.42 0l.07.07c.39.39.39 1.02 0 1.41l-5.92 5.94c-.41.39-1.04.39-1.44 0"
                              />
                            </svg>
                          </div>
                        )}

                        {/* Title (1 line max) */}
                        <div
                          className="dashboard-title"
                          style={{
                            fontSize: '13px',
                            fontWeight: 600,
                            color: '#212529',
                            lineHeight: '1.3',
                            flex: 1,
                            minWidth: 0,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            marginTop: '1px',
                            cursor: 'pointer',
                          }}
                          onClick={() => onDashboardClick?.(dashboard)}
                        >
                          {dashboard.name}
                        </div>
                      </div>

                      {/* Right side: Status + Favorite */}
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '6px',
                          flexShrink: 0,
                        }}
                      >
                        {/* Status */}
                        <span
                          style={{
                            transition: 'background-color 0.3s',
                            whiteSpace: 'nowrap',
                            cursor: 'default',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            backgroundColor: 'rgb(240, 240, 240)',
                            borderColor: 'transparent',
                            borderRadius: '21px',
                            padding: '0.35em 0.8em',
                            lineHeight: 1,
                            color: 'rgb(23, 23, 23)',
                            fontSize: '12px',
                            display: 'inline-block',
                            border: '1px solid transparent',
                          }}
                        >
                          {status.toLowerCase()}
                        </span>

                        {/* Favorite star */}
                        <span
                          role="img"
                          aria-label={
                            isFavorite
                              ? 'favorite-selected'
                              : 'favorite-unselected'
                          }
                          style={{
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                          onClick={e => {
                            e.stopPropagation();
                            onFavoriteToggle?.(dashboard);
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            fill="currentColor"
                            aria-hidden="true"
                            focusable="false"
                            viewBox="0 0 24 24"
                            style={{
                              color: isFavorite
                                ? 'rgb(251, 199, 0)'
                                : '#B2B2B2',
                            }}
                          >
                            <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M19.973 9.674a.8.8 0 0 0-.687-.525l-4.546-.651-2.037-4.056A.8.8 0 0 0 11.984 4a.8.8 0 0 0-.719.442L9.228 8.49l-4.546.659a.8.8 0 0 0-.647.533.77.77 0 0 0 .2.784l3.3 3.138-.8 4.455c-.058.3.067.606.32.784a.81.81 0 0 0 .839.055l4.09-2.094 4.074 2.102a.75.75 0 0 0 .368.094c.17 0 .334-.052.471-.149a.78.78 0 0 0 .32-.784l-.799-4.456 3.3-3.137a.78.78 0 0 0 .255-.8m-5.714 3.23.558 3.111-1.916-.989-.913-.47-2.855 1.46.37-2.06.19-1.06-2.239-2.128 2.06-.299 1.032-.15 1.437-2.855.97 1.931.47.934 3.08.441z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    {/* Tags */}
                    {tags && tags.length > 0 && (
                      <div style={{ marginBottom: '8px' }}>
                        <Space size="small">
                          {tags
                            .slice(0, 3)
                            .map((tag: string, tagIndex: number) => (
                              <Tag
                                key={tagIndex}
                                style={{
                                  margin: 0,
                                  background: '#F7F7F7',
                                  border: '1px solid #E0E0E0',
                                }}
                              >
                                {tag}
                              </Tag>
                            ))}
                        </Space>
                      </div>
                    )}
                  </div>

                  {/* Footer */}
                  {/* Hide footer when actions are not available for user */}
                  <div
                    style={{
                      fontSize: '11px',
                      color: '#adb5bd',
                      marginTop: '12px',
                      paddingTop: '8px',
                      borderTop: '1px solid #f1f3f4',
                    }}
                  >
                    <DashboardActionIcons
                      dashboard={dashboard}
                      onDeleteDashboard={onDeleteDashboard}
                      onUploadDashboard={onUploadDashboard}
                      onEditDashboard={onEditDashboard}
                    />
                  </div>
                </Card>
              </Col>
            );
          })}
        </Row>
      ) : (
        renderTableView()
      )}
    </div>
  );
};

export default DashboardsSection;
