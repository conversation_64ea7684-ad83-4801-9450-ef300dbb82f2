import { styled } from '@superset-ui/core';
import Badge from 'src/components/Badge';
import Icons from 'src/components/Icons';

const Styles = styled.div<{
  isSelected: boolean;
  businessColor: string;
  lightBusinessColor: string;
  indentLevel: number;
}>`
  position: relative;
  padding-block: ${({ theme }) => theme.gridUnit * 3}px;
  padding-inline: ${({ indentLevel, theme }) =>
      theme.gridUnit * 4 + indentLevel}px
    ${({ theme }) => theme.gridUnit * 4}px;
  cursor: pointer;
  background-color: ${({ isSelected, lightBusinessColor }) =>
    isSelected ? lightBusinessColor : 'transparent'};
  border-left: ${({ isSelected, businessColor }) =>
    isSelected ? `3px solid ${businessColor}` : '3px solid transparent'};
  border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ lightBusinessColor }) => lightBusinessColor};
  }

  .folder-content {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .folder-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background-color: transparent;
    border-radius: 2px;
    margin-right: 12px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: ${({ theme }) => theme.colors.grayscale.light3};
    }
  }
`;

const Folder = ({
  folderId,
  folderTitle,
  folderSubtitle,
  folderDashboardCount,
  isSelected,
  businessColor,
  lightBusinessColor,
  indentLevel,
  hasSubfolders,
  isExpanded,
  toggleFolderExpansion,
  setSelectedFolder,
}: {
  folderId: string;
  folderTitle: string;
  folderSubtitle: string;
  folderDashboardCount: number;
  isSelected: boolean;
  businessColor: string;
  lightBusinessColor: string;
  indentLevel: number;
  hasSubfolders: boolean;
  isExpanded: boolean;
  toggleFolderExpansion: (folderId: string) => void;
  setSelectedFolder: (folderId: string) => void;
}) => (
  <Styles
    onClick={() => setSelectedFolder(folderId)}
    isSelected={isSelected}
    businessColor={businessColor}
    lightBusinessColor={lightBusinessColor}
    indentLevel={indentLevel}
  >
    <div className="folder-content">
      <span
        role="button"
        tabIndex={0}
        className="folder-icon"
        onClick={
          hasSubfolders
            ? e => {
                e.stopPropagation();
                toggleFolderExpansion(folderId);
              }
            : undefined
        }
      >
        {hasSubfolders ? (
          // Triangle control for folders with subfolders
          isExpanded ? (
            <Icons.CaretDownOutlined
              style={{ fontSize: '12px', color: businessColor }}
            />
          ) : (
            <Icons.CaretRightOutlined
              style={{ fontSize: '12px', color: '#6c757d' }}
            />
          )
        ) : (
          // Folder icon for regular folders
          <Icons.FolderOutlined
            style={{
              fontSize: '10px',
              color: isSelected ? businessColor : '#6c757d',
            }}
          />
        )}
      </span>
      <div style={{ flex: 1 }}>
        <div
          style={{
            fontSize: '13px',
            fontWeight: isSelected ? 600 : 500,
            color: isSelected ? businessColor : '#212529',
            lineHeight: '1.2',
            marginBottom: '4px',
          }}
        >
          {folderTitle}
        </div>
        <div
          style={{
            fontSize: '11px',
            color: '#6c757d',
            lineHeight: '1.3',
          }}
        >
          {folderSubtitle}
        </div>
      </div>
      <Badge
        count={folderDashboardCount}
        size="default"
        style={{
          backgroundColor: isSelected ? businessColor : '#6c757d',
          fontSize: '10px',
        }}
      />
    </div>
  </Styles>
);

export default Folder;
