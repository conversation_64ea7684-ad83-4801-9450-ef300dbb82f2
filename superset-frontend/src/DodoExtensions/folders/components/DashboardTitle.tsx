import { NavLink } from 'react-router-dom';
import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Icons from 'src/components/Icons';
import HighlightedText from './HighlightedText';
import { Dashboard } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

const StyledNavLink = styled(NavLink)<{
  highlightOnHover?: boolean;
}>`
  text-decoration: none !important;
  color: ${({ theme }) => theme.colors.grayscale.base};
  padding: 4px 8px;
  border-radius: 4px;
  color: ${({ theme }) => theme.colors.grayscale.base};

  &:hover {
    color: ${({ highlightOnHover, theme }) =>
      highlightOnHover
        ? theme.colors.primary.base
        : theme.colors.grayscale.base};
  }

  &.active {
    background-color: ${({ theme }) => theme.colors.primary.light4};
  }
`;

const DashboardTitle = ({
  editMode,
  data,
  onDelete,
  onClick,
  searchTerm,
  showCertification = true,
  baseRoute = '/',
  highlightOnHover = false,
}: {
  editMode: boolean;
  data: Dashboard;
  onDelete?: (dashboard: Dashboard) => void;
  onClick?: () => void;
  searchTerm?: string;
  showCertification?: boolean;
  baseRoute?: string;
  highlightOnHover?: boolean;
}) => {
  const title =
    locale === 'ru'
      ? data.name_ru || data.name_en
      : data.name_en || data.name_ru;

  return editMode ? (
    <div className="item-title dashboard">
      <span>
        <HighlightedText text={title} searchTerm={searchTerm} />
      </span>
      {onDelete && (
        <span
          role="button"
          tabIndex={0}
          className="dash-delete-icon"
          onClick={e => {
            e.stopPropagation();
            onDelete(data);
          }}
          aria-label={t('Delete dashboard')}
        >
          <Icons.Trash iconSize="m" />
        </span>
      )}
    </div>
  ) : (
    <StyledNavLink
      to={`${baseRoute}${data.id}`}
      onClick={onClick}
      highlightOnHover={highlightOnHover}
      className={isActive => (isActive ? 'active' : '')}
    >
      {data.certified_by && showCertification && (
        <Icons.Certified className="certified-icon" iconSize="m" />
      )}
      <HighlightedText text={title} searchTerm={searchTerm} />
    </StyledNavLink>
  );
};

export default DashboardTitle;
