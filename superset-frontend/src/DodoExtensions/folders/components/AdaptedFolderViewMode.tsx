import { useSelector } from 'react-redux';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import Loading from 'src/components/Loading';
import { useFolder } from '../hooks/useFolder';
import { FolderSlug } from '../types';
import { DashboardFolders } from '../new_components';
import { createBusinessUnitFromFolders } from '../new_components/contants';

const AdaptedFolderViewMode = () => {
  const user = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );
  const isAdmin = Boolean(user.roles?.Admin);

  const { result: globalFolder, status: globalStatus } = useFolder(
    FolderSlug.Global,
  );
  // const { result: pluginFolder, status: pluginStatus } = useFolder(
  //   FolderSlug.Plugin,
  //   isAdmin,
  // );
  // const { result: personalFolder, status: personalStatus } = useFolder(
  //   FolderSlug.Personal,
  // );

  const businessData = createBusinessUnitFromFolders(
    globalFolder,
    // pluginFolder,
    // personalFolder,
    isAdmin,
  );

  // Loading state
  const isLoading = globalStatus === 'loading';
  // pluginStatus === 'loading' ||
  // personalStatus === 'loading';

  if (isLoading) {
    return <Loading />;
  }

  const handleDashboardClick = (dashboard: any) => {
    // Navigate to dashboard - this should be implemented based on your routing
    window.location.href = `/superset/dashboard/${dashboard.id}/`;
  };

  // Use the new UI components but with existing data
  return (
    <DashboardFolders
      businessData={businessData}
      onDashboardClick={handleDashboardClick}
      onFavoriteToggle={dashboard => {
        // TODO: Implement favorite toggle functionality
        console.log('Toggle favorite for dashboard:', dashboard.name);
      }}
      onDeleteDashboard={dashboard => {
        // TODO: Implement delete functionality
        console.log('Delete dashboard:', dashboard.name);
      }}
      onUploadDashboard={dashboard => {
        // TODO: Implement upload functionality
        console.log('Upload dashboard:', dashboard.name);
      }}
      onEditDashboard={dashboard => {
        // TODO: Implement edit functionality
        console.log('Edit dashboard:', dashboard.name);
      }}
    />
  );
};

export default AdaptedFolderViewMode;
