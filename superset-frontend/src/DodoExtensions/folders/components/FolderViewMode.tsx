import cx from 'classnames';
import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import { useSelector } from 'react-redux';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import FolderCard from './FolderCard';
import { useFolder } from '../hooks/useFolder';
import { FolderSlug } from '../types';

const StyledContainer = styled.div`
  margin-inline: ${({ theme }) => theme.gridUnit * 4}px;
  display: flex;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  flex: 1;

  .cards-container {
    margin-block: ${({ theme }) => theme.gridUnit * 5 + 1}px;
    display: grid;
    grid-template-columns: repeat(3, minmax(250px, 1fr));
    gap: ${({ theme }) => theme.gridUnit * 4}px;
    flex: 1;
  }
`;

const ViewModeContainer = styled.div`
  padding-right: ${({ theme }) => theme.gridUnit * 4}px;
  margin-top: ${({ theme }) => theme.gridUnit * 5 + 1}px;
  white-space: nowrap;
  display: flex;

  .toggle-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    border-radius: ${({ theme }) => theme.gridUnit / 2}px;
    /* padding: ${({ theme }) => theme.gridUnit}px; */
    /* padding-bottom: ${({ theme }) => theme.gridUnit * 0.5}px; */

    &:first-of-type {
      margin-right: ${({ theme }) => theme.gridUnit * 2}px;
    }
  }

  .anticon {
    line-height: 0;
  }

  .active {
    background-color: ${({ theme }) => theme.colors.grayscale.base};
    svg {
      color: ${({ theme }) => theme.colors.grayscale.light5};
    }
  }
`;

const ViewModeToggle = ({
  setFolderMode,
}: {
  setFolderMode: (value: boolean) => void;
}) => (
  <ViewModeContainer>
    <div
      role="button"
      tabIndex={0}
      className={cx('toggle-button', { active: true })}
    >
      <Icons.FolderOpenOutlined />
    </div>
    <div
      role="button"
      tabIndex={0}
      onClick={e => {
        e.currentTarget.blur();
        setFolderMode(false);
      }}
      className={cx('toggle-button')}
    >
      <Icons.ListView />
    </div>
  </ViewModeContainer>
);

interface IProps {
  setFolderMode: (value: boolean) => void;
}

const FolderViewMode = ({ setFolderMode }: IProps) => {
  const user = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );
  const isAdmin = Boolean(user.roles?.Admin);

  const { result: globalFolder, status: globalStatus } = useFolder(
    FolderSlug.Global,
  );
  const { result: pluginFolder, status: pluginStatus } = useFolder(
    FolderSlug.Plugin,
    isAdmin,
  );
  const { result: personalFolder, status: personalStatus } = useFolder(
    FolderSlug.Personal,
  );

  return (
    <StyledContainer>
      <ViewModeToggle setFolderMode={setFolderMode} />
      <div className="cards-container">
        <FolderCard
          title={t('Global folder')}
          slug={FolderSlug.Global}
          rootFolderId={globalFolder?.id || 0}
          // browse="/dashboard/list/"
          canEdit={isAdmin}
          content={globalFolder?.children || []}
          editMode={false}
          loading={globalStatus === 'loading'}
        />
        {isAdmin && (
          <FolderCard
            title={t('Office manager folder')}
            slug={FolderSlug.Plugin}
            rootFolderId={pluginFolder?.id || 0}
            // browse="/dashboard/list/"
            canEdit
            content={pluginFolder?.children || []}
            editMode={false}
            loading={pluginStatus === 'loading'}
          />
        )}
        <FolderCard
          title={t('Personal folder')}
          slug={FolderSlug.Personal}
          rootFolderId={personalFolder?.id || 0}
          // browse="/dashboard/list/"
          canEdit
          content={personalFolder?.children || []}
          editMode={false}
          loading={personalStatus === 'loading'}
        />
        {/* <FolderCard
          title={t('Team folders')}
          browse="/dashboard/list/"
          canEdit
          folderData={data.team}
          editMode={false}
          loading={false}
        /> */}
      </div>
    </StyledContainer>
  );
};

export default FolderViewMode;
