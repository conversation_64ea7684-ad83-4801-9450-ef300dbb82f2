import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '@superset-ui/core';
import { theme } from 'src/preamble';
import AdaptedFolderViewMode from './AdaptedFolderViewMode';

// Mock the useFolder hook
jest.mock('../hooks/useFolder', () => ({
  useFolder: jest.fn(() => ({
    result: {
      id: 1,
      name_en: 'Test Folder',
      name_ru: 'Тестовая папка',
      description_en: 'Test description',
      description_ru: 'Тестовое описание',
      children: [
        {
          id: 1,
          item_type: 1, // Dashboard
          name_en: 'Test Dashboard',
          name_ru: 'Тестовый дашборд',
          certified_by: 'admin',
        },
      ],
    },
    status: 'complete',
    error: null,
  })),
}));

// Mock the new components
jest.mock('../new_components', () => ({
  DashboardFolders: ({ businessData, onDashboardClick }: any) => (
    <div data-testid="dashboard-folders">
      <div>Business Units: {businessData?.length || 0}</div>
      <button onClick={() => onDashboardClick?.({ id: '1', name: 'Test Dashboard' })}>
        Test Dashboard
      </button>
    </div>
  ),
}));

const mockStore = configureStore({
  reducer: {
    user: () => ({
      roles: { Admin: true },
    }),
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('AdaptedFolderViewMode', () => {
  const mockSetFolderMode = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    expect(screen.getByTestId('dashboard-folders')).toBeInTheDocument();
  });

  it('displays business units count', () => {
    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    expect(screen.getByText('Business Units: 1')).toBeInTheDocument();
  });

  it('handles dashboard click', () => {
    // Mock window.location.href
    delete (window as any).location;
    window.location = { href: '' } as any;

    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    
    const dashboardButton = screen.getByText('Test Dashboard');
    dashboardButton.click();

    // The component should attempt to navigate to the dashboard
    expect(window.location.href).toBe('/superset/dashboard/1/');
  });

  it('shows loading state when data is loading', () => {
    // Mock loading state
    const useFolder = require('../hooks/useFolder').useFolder;
    useFolder.mockReturnValue({
      result: null,
      status: 'loading',
      error: null,
    });

    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    expect(screen.getByText('Loading folders...')).toBeInTheDocument();
  });

  it('shows no folders message when no data available', () => {
    // Mock empty state
    const useFolder = require('../hooks/useFolder').useFolder;
    useFolder.mockReturnValue({
      result: null,
      status: 'complete',
      error: null,
    });

    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    expect(screen.getByText('No folders available')).toBeInTheDocument();
  });

  it('calls setFolderMode when toggle button is clicked', () => {
    renderWithProviders(<AdaptedFolderViewMode setFolderMode={mockSetFolderMode} />);
    
    // Find the list view toggle button and click it
    const toggleButtons = screen.getAllByRole('button');
    const listViewToggle = toggleButtons.find(button => 
      button.getAttribute('tabIndex') === '0'
    );
    
    if (listViewToggle) {
      listViewToggle.click();
      expect(mockSetFolderMode).toHaveBeenCalledWith(false);
    }
  });
});
