import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Modal from 'src/components/Modal';
import { Dashboard } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

const ModalContent = styled.div`
  padding: 16px 0;
`;

interface DeleteConfirmModalProps {
  show: boolean;
  dashboard: Dashboard | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  show,
  dashboard,
  onConfirm,
  onCancel,
}) => {
  if (!dashboard) return null;

  const dashboardTitle =
    locale === 'ru'
      ? dashboard.name_ru || dashboard.name_en
      : dashboard.name_en || dashboard.name_ru;

  return (
    <Modal
      title={t('Delete dashboard')}
      show={show}
      onHide={onCancel}
      onHandledPrimaryAction={onConfirm}
      primaryButtonName={t('Delete')}
      primaryButtonType="danger"
      width="500px"
    >
      <ModalContent>
        <p>
          {t('Are you sure you want to exclude')} <b>{dashboardTitle}</b>{' '}
          {t('from the folder?')}
        </p>
      </ModalContent>
    </Modal>
  );
};

export default DeleteConfirmModal;
