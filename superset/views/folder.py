from flask import redirect, url_for
from flask_appbuilder import expose

from superset.superset_typing import FlaskResponse
from superset.views.base import BaseSupersetView


class FolderModelView(BaseSupersetView):
    route_base = "/folder"
    class_permission_name = "Folder"

    @expose("/")
    def index(self) -> FlaskResponse:
        return redirect(url_for("FolderModelView.get", pk="personal"))

    @expose("/<string:pk>/")
    def get(self, pk: str) -> FlaskResponse:  # pylint: disable=unused-argument
        return super().render_app_template()
