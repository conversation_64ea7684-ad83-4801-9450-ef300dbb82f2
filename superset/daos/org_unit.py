import logging
from typing import Any

from superset import db
from superset.daos.base import BaseDAO
from superset.orgstructure.models import ClaimType, OrgUnit
from superset.orgstructure.utils import parse_claim

logger = logging.getLogger(__name__)


class OrgUnitDAO(BaseDAO[OrgUnit]):
    model_cls = OrgUnit

    @staticmethod
    def upsert_from_payload(unit: dict[str, Any]) -> OrgUnit | None:
        try:
            org_unit_obj: OrgUnit | None = (
                db.session.query(OrgUnit).filter(OrgUnit.id == unit["id"]).one_or_none()
            )
            if org_unit_obj and org_unit_obj.version >= unit["version"]:
                return org_unit_obj

            if org_unit_obj is None:
                org_unit_obj = OrgUnit(id=unit["id"])
                db.session.add(org_unit_obj)

            for key, value in unit.items():
                setattr(org_unit_obj, key, value)

            db.session.commit()
            return org_unit_obj
        except Exception as ex:
            logger.exception("Failed to upsert OrgUnit: %s", ex)
            try:
                db.session.rollback()
            except Exception:
                pass
            return None

    @staticmethod
    def get_departments_by_country_ids(
        country_ids: list[int], monolith_id: str | None
    ) -> list[str]:
        if not country_ids or not monolith_id:
            return []
        query = db.session.query(OrgUnit.department_uuid).filter(
            OrgUnit.monolith_id == monolith_id,
            OrgUnit.country_id.in_(country_ids),
            OrgUnit.is_removed.is_(False),
        )
        return [dept_uuid for (dept_uuid,) in query.distinct().all() if dept_uuid]

    @staticmethod
    def get_departments_from_claims(
        dobr: list[str],
        monolith_id: str | None,
    ) -> list[str]:
        if not dobr:
            return []

        department_ids: list[str] = []
        country_ids: list[int] = []

        for claim in dobr:
            if not (parsed_claim := parse_claim(claim)):
                continue
            uuid, claim_type, _role = parsed_claim

            if claim_type == ClaimType.DEPARTMENT:
                department_ids.append(uuid)
            elif claim_type == ClaimType.COUNTRY:
                country_ids.append(int(uuid, 16))
            elif claim_type == ClaimType.UNIT:
                pass

        return list(
            set(
                department_ids
                + OrgUnitDAO.get_departments_by_country_ids(country_ids, monolith_id)
            )
        )
