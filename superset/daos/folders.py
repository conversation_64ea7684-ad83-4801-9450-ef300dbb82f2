from typing import Any

from sqlalchemy import and_, or_

from superset import db
from superset.daos.base import BaseDAO
from superset.folders.models import (
    Folder,
    FolderItem,
    FolderItemType,
    FolderLevel,
    FolderType,
)
from superset.models.dashboard import Dashboard


class FolderDAO(BaseDAO[Folder]):
    @staticmethod
    def get_by_type(
        user_id: int, folder_type: FolderType, team_id: int | None = None
    ) -> list[Folder]:
        folders = (
            db.session.query(Folder)
            .filter(
                or_(
                    Folder.type == FolderType.GLOBAL.value,
                    Folder.type == FolderType.PLUGIN.value,
                    and_(
                        Folder.type == FolderType.TEAM.value, Folder.team_id == team_id
                    ),
                    # pylint: disable=comparison-with-callable
                    and_(
                        Folder.type == FolderType.PERSONAL.value,
                        Folder.created_by_fk == user_id,
                    ),
                    Folder.level != FolderLevel.USER.value,
                ),
                Folder.type == folder_type.value,
            )
            .order_by(Folder.order)
            .all()
        )
        return folders

    @staticmethod
    def get_by_parent_id(parent_id: int) -> list[Folder]:
        folders = (
            db.session.query(Folder)
            .filter(Folder.parent_id == parent_id)
            .order_by(Folder.order)
            .all()
        )
        return folders

    @classmethod
    def create_last_in_order(
        cls,
        folder_data: dict[str, Any],
        parent_folder: Folder,
        user_id: int,
        team_id: int | None = None,
    ) -> Folder:
        siblings = FolderDAO.get_by_parent_id(folder_data["parent_id"])
        items = FolderItemDAO.get_by_folders(
            [parent_folder.id], user_id=user_id, team_id=team_id
        )
        max_order = (
            max(sibling.order for sibling in siblings + items)
            if siblings + items
            else -1
        )
        folder_data["order"] = max_order + 1

        folder_data["type"] = parent_folder.type
        folder_data["team_id"] = (
            team_id if parent_folder.type == FolderType.TEAM else None
        )

        folder = super().create(attributes=folder_data)
        return folder

    @classmethod
    def delete(cls, items: list[Folder]) -> None:
        """
        Delete folders and let the database handle CASCADE deletion
        """
        for item in items:
            db.session.query(Folder).filter(Folder.id == item.id).delete()


class FolderItemDAO(BaseDAO[FolderItem]):
    @staticmethod
    def get_by_folders(
        folders: list[int], user_id: int, team_id: int | None = None
    ) -> list[FolderItem | Dashboard]:
        query = db.session.query(
            # FolderItem.id,
            FolderItem.folder_id,
            FolderItem.item_id,
            FolderItem.item_type,
            FolderItem.order,
            Dashboard.dashboard_title,
            Dashboard.dashboard_title_ru,
            Dashboard.certified_by,
        ).select_from(FolderItem)

        query = query.join(Folder, Folder.id == FolderItem.folder_id)

        query = query.filter(
            or_(
                Folder.type == FolderType.GLOBAL.value,
                Folder.type == FolderType.PLUGIN.value,
                and_(Folder.type == FolderType.TEAM.value, Folder.team_id == team_id),
                # pylint: disable=comparison-with-callable
                and_(
                    Folder.type == FolderType.PERSONAL.value,
                    Folder.created_by_fk == user_id,
                ),
                Folder.level != FolderLevel.USER.value,
            ),
            FolderItem.folder_id.in_(folders),
        )

        query = query.join(
            Dashboard,
            (Dashboard.id == FolderItem.item_id)
            & (FolderItem.item_type == FolderItemType.DASHBOARD.value),
        )

        return query.all()

    @staticmethod
    def delete_by_item_ids(item_ids: list[int], item_type: int) -> None:
        """Delete all FolderItems that reference the given dashboard IDs"""
        db.session.query(FolderItem).filter(
            FolderItem.item_id.in_(item_ids), FolderItem.item_type == item_type
        ).delete(synchronize_session=False)

    @staticmethod
    def find(folder_id: int, item_id: int, item_type: int) -> FolderItem | None:
        return (
            db.session.query(FolderItem)
            .filter(
                FolderItem.folder_id == folder_id,
                FolderItem.item_id == item_id,
                FolderItem.item_type == item_type,
            )
            .one_or_none()
        )
