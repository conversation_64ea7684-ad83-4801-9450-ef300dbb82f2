# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe
from flask_appbuilder.security.sqla.models import User
from marshmallow import ValidationError

from superset import db
from superset.daos.folders import FolderDAO, FolderItemDAO
from superset.daos.team import TeamDAO
from superset.folders.schemas import (
    FolderContentItemSchema,
    FolderCreateSchema,
    FolderTreeSchema,
    FolderUpdateSchema,
    openapi_spec_methods_override,
)
from superset.folders.security import (
    can_modify_folder,
    can_update_folder_content,
)
from superset.folders.utils import (
    add_order_to_content,
    calculate_folder_diff,
    get_folder_tree,
    reorder_folder_items,
    validate_folder_content,
)
from superset.utils.core import get_user
from superset.views.base_api import BaseSupersetApi, requires_json

logger = logging.getLogger(__name__)


class FolderRestApi(BaseSupersetApi):
    """
    Folder REST API for managing folders
    """

    resource_name = "folder"
    allow_browser_login = True
    openapi_spec_tag = "Folder"
    openapi_spec_methods = openapi_spec_methods_override

    @expose("/<string:slug>", methods=("GET",))
    @protect()
    @safe
    def get_by_slug(self, slug: str) -> Response:
        user: User = get_user()
        user_team = TeamDAO.get_team_by_user_id(user.id)

        folder = FolderDAO.find_one_or_none(slug=slug)
        if not folder:
            return self.response_404()

        folder_tree = get_folder_tree(
            folder, user.id, user_team.id if user_team else None
        )

        schema = FolderTreeSchema()
        return self.response(200, folder=schema.dump(obj=folder_tree))

    @expose("/", methods=("POST",))
    @protect()
    @safe
    @requires_json
    def create(self) -> Response:
        """
        Create a new folder.
        ---
        post:
          summary: Create a new folder
          requestBody:
            description: Folder schema
            required: true
            content:
              application/json:
                schema: FolderCreateSchema
          responses:
            200:
              description: Folder created
              content:
                application/json:
                  schema: FolderTreeSchema
        """
        try:
            folder_schema = FolderCreateSchema()
            folder_data = folder_schema.load(request.json)

            user: User = get_user()
            user_team = TeamDAO.get_team_by_user_id(user.id)

            parent_folder = FolderDAO.find_by_id(folder_data["parent_id"])
            if not parent_folder:
                return self.response_400(message="Parent folder not found")

            if not can_update_folder_content(parent_folder, user):
                return self.response_400(
                    message="You are not allowed to create a folder in this parent folder"
                )

            FolderDAO.create_last_in_order(
                folder_data=folder_data,
                parent_folder=parent_folder,
                user_id=user.id,
                team_id=user_team.id if user_team else None,
            )
            db.session.commit()  # pylint: disable=consider-using-transaction

            folder_tree = get_folder_tree(
                parent_folder, user.id, user_team.id if user_team else None
            )

            schema = FolderTreeSchema()
            return self.response(200, folder=schema.dump(obj=folder_tree))
        except ValidationError as error:
            return self.response_400(message=error.messages)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            return self.response_400(message=str(ex))

    @expose("/<int:folder_id>", methods=("PUT",))
    @protect()
    @safe
    @requires_json
    def update(self, folder_id: int) -> Response:
        """
        Update a folder.
        ---
        put:
          summary: Update a folder
          requestBody:
            description: Folder schema
            required: true
            content:
              application/json:
                schema: FolderUpdateSchema
          responses:
            200:
              description: Folder updated
              content:
                application/json:
                  schema: FolderTreeSchema
        """
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user: User = get_user()

            if not can_modify_folder(folder, user):
                return self.response_400(
                    message="You are not allowed to update this folder"
                )

            schema = FolderUpdateSchema()
            data = schema.load(request.json)

            folder = FolderDAO.update(item=folder, attributes=data)
            db.session.commit()  # pylint: disable=consider-using-transaction

            schema = FolderTreeSchema()
            return self.response(200, folder=schema.dump(obj=folder))
        except Exception as ex:  # pylint: disable=broad-exception-caught
            return self.response_400(message=str(ex))

    @expose("/<int:folder_id>/content", methods=("PUT",))
    @protect()
    @safe
    @requires_json
    def update_content(self, folder_id: int) -> Response:
        """
        Update the content of a folder.
        ---
        put:
          summary: Update the content of a folder
          requestBody:
            description: Folder content schema
            required: true
            content:
              application/json:
                schema: FolderContentItemSchema
          responses:
            200:
              description: Folder content updated
              content:
                application/json:
                  schema: FolderTreeSchema
        """
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user: User = get_user()

            if not can_update_folder_content(folder, user):
                return self.response_400(
                    message="You are not allowed to update the content of this folder"
                )

            user_team = TeamDAO.get_team_by_user_id(user.id)

            child_folders = FolderDAO.get_by_parent_id(folder_id)
            items = FolderItemDAO.get_by_folders(
                [folder_id],
                user_id=user.id,
                team_id=user_team.id if user_team else None,
            )

            current_content = FolderContentItemSchema().dump(
                child_folders + items, many=True
            )

            new_content = FolderContentItemSchema().load(request.json, many=True)
            validate_folder_content(folder, new_content, user)
            new_content = add_order_to_content(new_content)

            changes = calculate_folder_diff(current_content, new_content)
            reorder_folder_items(folder_id, changes)

            folder_tree = get_folder_tree(
                folder, user.id, user_team.id if user_team else None
            )

            schema = FolderTreeSchema()
            return self.response(200, folder=schema.dump(obj=folder_tree))
        except Exception as ex:  # pylint: disable=broad-exception-caught
            return self.response_400(message=str(ex))

    @expose("/<int:folder_id>", methods=("DELETE",))
    @protect()
    @safe
    def delete(self, folder_id: int) -> Response:
        """
        Delete a folder.
        ---
        delete:
          summary: Delete a folder
          parameters:
          - in: path
            schema:
              type: integer
            name: folder_id
          responses:
            200:
              description: Folder deleted
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      message:
                        type: string
        """
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user: User = get_user()

            if not can_modify_folder(folder, user):
                return self.response_400(
                    message="You are not allowed to delete this folder"
                )

            FolderDAO.delete([folder])
            db.session.commit()  # pylint: disable=consider-using-transaction

            return self.response(200, message="Folder deleted")
        except Exception as ex:  # pylint: disable=broad-exception-caught
            return self.response_400(message=str(ex))
